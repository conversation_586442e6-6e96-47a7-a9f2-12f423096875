import { defineConfig } from 'vite';
import solidPlugin from 'vite-plugin-solid';
import { resolve } from 'path';
import { copyFileSync, mkdirSync, existsSync } from 'fs';

function copyAssets() {
  return {
    name: 'copy-assets',
    closeBundle() {
      try {
        // 创建必要的目录
        ['dist', 'dist/assets', 'dist/assets/icons'].forEach(dir => {
          if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
          }
        });

        // 复制清单文件
        copyFileSync(
          resolve(__dirname, 'src/manifest.json'),
          resolve(__dirname, 'dist/manifest.json')
        );

         // 复制rule
         copyFileSync(
            resolve(__dirname, 'src/rules.json'),
            resolve(__dirname, 'dist/rules.json')
          );

        // 复制后台脚本
        copyFileSync(
          resolve(__dirname, 'src/background.js'),
          resolve(__dirname, 'dist/background.js')
        );

        // 复制content script
        copyFileSync(
          resolve(__dirname, 'src/content.js'),
          resolve(__dirname, 'dist/content.js')
        );


        // 复制sql-wasm.wasm
        copyFileSync(
          resolve(__dirname, 'src/assets/sql-wasm.wasm'),
          resolve(__dirname, 'dist/assets/sql-wasm.wasm')
        );
        // 复制jcr.db
        copyFileSync(
          resolve(__dirname, 'src/assets/jcr.db'),
          resolve(__dirname, 'dist/assets/jcr.db')
        );

        // 复制图标文件
        const iconSizes = ['16', '32', '48', '128'];
        iconSizes.forEach(size => {
          copyFileSync(
            resolve(__dirname, `src/assets/icons/icon${size}.png`),
            resolve(__dirname, `dist/assets/icons/icon${size}.png`)
          );
        });

        console.log('✓ 资源文件复制完成');
      } catch (error) {
        console.error('处理文件失败:', error);
        throw error;
      }
    }
  };
}

export default defineConfig({
  plugins: [solidPlugin(), copyAssets()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  build: {
    target: 'esnext',
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        index: resolve(__dirname, 'index.html')
      }
    },
    sourcemap: true,
    minify: false
  }
});