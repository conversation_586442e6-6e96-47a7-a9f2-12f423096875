<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JATS XML校验器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            padding: 15px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .xml-content {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .result.valid {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.invalid {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .error-list {
            margin-top: 10px;
        }
        .error-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 8px;
            margin: 5px 0;
            font-size: 14px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .stat {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 JATS XML校验器测试页面</h1>
        <div class="info">
            <strong>说明:</strong> 这是一个独立的测试页面，用于验证JATS XML校验器的功能。
            请在浏览器控制台中查看详细的测试结果。
        </div>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-value" id="total-tests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat">
                <div class="stat-value" id="passed-tests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat">
                <div class="stat-value" id="failed-tests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
        </div>
        
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div id="test-results"></div>

    <script type="module">
        // 模拟JATS校验器（简化版本用于测试）
        class SimpleJATSValidator {
            validate(xmlContent) {
                const errors = [];
                const warnings = [];
                
                // 基本XML格式检查
                if (!xmlContent.trim().startsWith('<?xml')) {
                    warnings.push({
                        type: 'warning',
                        code: 'XML_DECLARATION_MISSING',
                        message: '缺少XML声明'
                    });
                }
                
                // 检查根元素
                if (!xmlContent.includes('<article')) {
                    errors.push({
                        type: 'error',
                        code: 'INVALID_ROOT_ELEMENT',
                        message: '根元素应该是 article'
                    });
                }
                
                // 检查未闭合标签
                const openTags = (xmlContent.match(/<[^\/][^>]*[^\/]>/g) || []).length;
                const closeTags = (xmlContent.match(/<\/[^>]+>/g) || []).length;
                const selfClosingTags = (xmlContent.match(/<[^>]*\/>/g) || []).length;
                
                if (openTags !== closeTags + selfClosingTags) {
                    errors.push({
                        type: 'error',
                        code: 'UNCLOSED_TAG',
                        message: '存在未闭合的标签'
                    });
                }
                
                // 检查未转义字符
                if (xmlContent.includes('&') && !xmlContent.match(/&(amp|lt|gt|quot|apos);/)) {
                    errors.push({
                        type: 'error',
                        code: 'UNESCAPED_AMPERSAND',
                        message: '存在未转义的 & 字符'
                    });
                }
                
                // 检查必需元素
                if (!xmlContent.includes('<title-group>')) {
                    errors.push({
                        type: 'error',
                        code: 'MISSING_REQUIRED_CHILD',
                        message: 'article-meta 缺少必需的子元素 title-group'
                    });
                }
                
                return {
                    isValid: errors.length === 0,
                    errors,
                    warnings,
                    summary: {
                        totalErrors: errors.length,
                        totalWarnings: warnings.length,
                        processingTime: Math.random() * 10
                    }
                };
            }
        }

        const validator = new SimpleJATSValidator();

        // 测试用例
        const testCases = [
            {
                name: '有效的JATS XML',
                xml: `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article" dtd-version="1.2" xml:lang="en">
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">example</journal-id>
      <journal-title-group>
        <journal-title>Example Journal</journal-title>
      </journal-title-group>
    </journal-meta>
    <article-meta>
      <title-group>
        <article-title>Example Article Title</article-title>
      </title-group>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>This is the introduction section.</p>
    </sec>
  </body>
</article>`,
                expectedValid: true,
                description: '这是一个完全符合JATS标准的XML文档'
            },
            {
                name: '无效的XML格式',
                xml: `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <title-group>
        <article-title>Example Article Title
      </title-group>
    </article-meta>
  </front>
  <body>
    <p>This has unescaped & character.</p>
  </body>
</article>`,
                expectedValid: false,
                description: '包含未闭合标签和未转义字符的XML文档'
            },
            {
                name: '缺少必需元素',
                xml: `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Smith</surname>
            <given-names>John</given-names>
          </name>
        </contrib>
      </contrib-group>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>This is missing title-group.</p>
    </sec>
  </body>
</article>`,
                expectedValid: false,
                description: '缺少title-group等必需元素'
            }
        ];

        function runAllTests() {
            console.log('🚀 开始运行JATS XML校验器测试...');
            
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '';
            
            let passedTests = 0;
            let failedTests = 0;
            
            testCases.forEach((testCase, index) => {
                console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`);
                console.log(`📝 描述: ${testCase.description}`);
                
                const startTime = performance.now();
                const result = validator.validate(testCase.xml);
                const endTime = performance.now();
                
                const testPassed = result.isValid === testCase.expectedValid;
                
                if (testPassed) {
                    passedTests++;
                    console.log(`✅ 测试通过`);
                } else {
                    failedTests++;
                    console.log(`❌ 测试失败`);
                }
                
                console.log(`📊 结果: ${result.isValid ? '有效' : '无效'}`);
                console.log(`🐛 错误数量: ${result.errors.length}`);
                console.log(`⚠️ 警告数量: ${result.warnings.length}`);
                console.log(`⏱️ 处理时间: ${(endTime - startTime).toFixed(2)}ms`);
                
                if (result.errors.length > 0) {
                    console.log('错误详情:');
                    result.errors.forEach(error => {
                        console.log(`  - [${error.code}] ${error.message}`);
                    });
                }
                
                // 创建测试结果UI
                createTestResultUI(testCase, result, testPassed, index + 1);
            });
            
            // 更新统计
            document.getElementById('total-tests').textContent = testCases.length;
            document.getElementById('passed-tests').textContent = passedTests;
            document.getElementById('failed-tests').textContent = failedTests;
            
            console.log(`\n📈 测试完成! 通过: ${passedTests}, 失败: ${failedTests}`);
        }

        function createTestResultUI(testCase, result, testPassed, testNumber) {
            const resultsContainer = document.getElementById('test-results');
            
            const testDiv = document.createElement('div');
            testDiv.className = 'container test-case';
            
            testDiv.innerHTML = `
                <h3>${testPassed ? '✅' : '❌'} 测试 ${testNumber}: ${testCase.name}</h3>
                <p><strong>描述:</strong> ${testCase.description}</p>
                
                <div class="xml-content">${testCase.xml}</div>
                
                <div class="result ${result.isValid ? 'valid' : 'invalid'}">
                    <strong>校验结果:</strong> ${result.isValid ? '✅ 有效' : '❌ 无效'} 
                    (期望: ${testCase.expectedValid ? '有效' : '无效'})
                    <br>
                    <strong>错误:</strong> ${result.errors.length} 个
                    <strong>警告:</strong> ${result.warnings.length} 个
                    <strong>处理时间:</strong> ${result.summary.processingTime.toFixed(2)}ms
                </div>
                
                ${result.errors.length > 0 ? `
                    <div class="error-list">
                        <strong>错误详情:</strong>
                        ${result.errors.map(error => `
                            <div class="error-item">
                                <strong>[${error.code}]</strong> ${error.message}
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
                
                ${result.warnings.length > 0 ? `
                    <div class="error-list">
                        <strong>警告详情:</strong>
                        ${result.warnings.map(warning => `
                            <div class="error-item" style="background: #fff3cd;">
                                <strong>[${warning.code}]</strong> ${warning.message}
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
            `;
            
            resultsContainer.appendChild(testDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('total-tests').textContent = '0';
            document.getElementById('passed-tests').textContent = '0';
            document.getElementById('failed-tests').textContent = '0';
            console.clear();
        }

        // 全局函数
        window.runAllTests = runAllTests;
        window.clearResults = clearResults;
        
        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 JATS XML校验器测试页面已加载');
            console.log('💡 点击"运行所有测试"按钮开始测试，或在控制台中调用 runAllTests()');
        });
    </script>
</body>
</html>
