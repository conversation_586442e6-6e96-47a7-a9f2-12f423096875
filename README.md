# Chrome 扩展工具集合

该项目是一个 Chrome 扩展，提供一个工具集合，用户可以通过功能菜单访问不同的工具。扩展的界面分为左侧的功能菜单和右侧的功能界面，支持跨域请求和反向代理 API。

## 项目结构

```
chrome-extension
├── src
│   ├── background.js        // 后台逻辑，管理事件和跨域请求
│   ├── content.js          // 与网页内容交互，注入脚本和样式
│   ├── popup
│   │   ├── popup.html      // 弹出窗口的HTML结构
│   │   ├── popup.js        // 弹出窗口的逻辑
│   │   └── popup.css       // 弹出窗口的样式
│   ├── options
│   │   ├── options.html    // 选项页面的HTML结构
│   │   ├── options.js      // 选项页面的逻辑
│   │   └── options.css     // 选项页面的样式
│   ├── pages
│   │   ├── index.html      // 主功能页面的HTML结构
│   │   ├── index.js        // 主功能页面的逻辑
│   │   └── index.css       // 主功能页面的样式
│   └── manifest.json       // 扩展的配置文件
├── package.json             // npm的配置文件
└── README.md                // 项目的文档和使用说明
```

## 安装与使用

1. 下载或克隆该项目。
2. 在 Chrome 浏览器中打开 `chrome://extensions/`。
3. 开启开发者模式。
4. 点击“加载已解压的扩展程序”，选择项目的根目录。
5. 扩展加载完成后，您可以通过点击扩展图标访问工具集合。

## 功能说明

- **功能菜单**：左侧展示所有可用工具，用户可以点击选择。
- **功能界面**：右侧展示所选工具的具体功能和操作界面。
- **跨域请求**：支持从不同域名获取数据，确保工具的功能完整性。
- **反向代理 API**：可以通过配置实现 API 的反向代理，提升请求的灵活性。

## 贡献

欢迎任何形式的贡献！请提交问题或拉取请求。

## 许可证

该项目遵循 MIT 许可证。