{"name": "chrome-extension", "version": "0.0.8", "description": "一个工具集合的Chrome扩展，支持跨域请求和反代API。", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "watch": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@solidjs/router": "^0.15.3", "better-sqlite3": "^11.8.1", "fast-xml-parser": "^5.0.8", "jszip": "^3.10.1", "mammoth": "^1.9.0", "solid-js": "^1.8.15", "sql.js": "^1.12.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.12", "@types/chrome": "^0.0.304", "@types/node": "^22.13.10", "@types/sql.js": "^1.4.9", "autoprefixer": "^10.4.17", "daisyui": "^4.7.2", "postcss": "^8.4.35", "rimraf": "^5.0.10", "tailwindcss": "^3.4.1", "vite": "^5.1.4", "vite-plugin-solid": "^2.10.1"}}