import { createEffect, createSignal } from 'solid-js';

interface RenameFormData {
    prefix: string;
    suffix: string;
    startNumber: number | null;
    extension: string; // Target extension to change
    newExtension: string; // New extension to apply
}

interface FileInfo {
    path: string; // Relative path from the root selected directory
    name: string; // Original file name with extension
    handle: FileSystemFileHandle;
    parentDir: FileSystemDirectoryHandle; // Handle of the direct parent directory
}

interface RenameResult {
    success: boolean;
    originalPath: string;
    newPath?: string; // Full relative new path
    error?: Error;
    skipped?: boolean;
}

export default function BatchRename() {
    const [formData, setFormData] = createSignal<RenameFormData>({
        prefix: '',
        suffix: '',
        startNumber: null,
        extension: '',
        newExtension: ''
    });

    const [files, setFiles] = createSignal<FileInfo[]>([]);
    const [previewResults, setPreviewResults] = createSignal<{ original: string; new: string }[]>([]);
    const [includeSubfolders, setIncludeSubfolders] = createSignal(false);
    const [allowOverwrite, setAllowOverwrite] = createSignal(true);
    const [processing, setProcessing] = createSignal(false);
    const [rootDirectoryHandle, setRootDirectoryHandle] = createSignal<FileSystemDirectoryHandle | null>(null);

    const getNameAndExtension = (filename: string): { namePart: string; extPart: string } => {
        const lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex === -1 || lastDotIndex === 0 || lastDotIndex === filename.length - 1) {
            return { namePart: filename, extPart: '' };
        }
        return {
            namePart: filename.substring(0, lastDotIndex),
            extPart: filename.substring(lastDotIndex + 1)
        };
    };

    const processDirectory = async (dirHandle: FileSystemDirectoryHandle, currentPath = ''): Promise<FileInfo[]> => {
        const fileInfos: FileInfo[] = [];
        try {
            for await (const entry of dirHandle.values()) {
                const entryPath = currentPath ? `${currentPath}/${entry.name}` : entry.name;
                if (entry.kind === 'file') {
                    fileInfos.push({
                        path: entryPath,
                        name: entry.name,
                        handle: entry as FileSystemFileHandle,
                        parentDir: dirHandle
                    });
                } else if (entry.kind === 'directory' && includeSubfolders()) {
                    try {
                        const subDirHandle = await dirHandle.getDirectoryHandle(entry.name);
                        const subFiles = await processDirectory(subDirHandle, entryPath);
                        fileInfos.push(...subFiles);
                    } catch (subDirError: any) {
                        console.warn(`无法访问子目录 "${entryPath}": ${subDirError.message}. 跳过该目录.`);
                    }
                }
            }
        } catch (error: any) {
            console.error(`处理目录 "${currentPath || dirHandle.name}" 时出错: ${error.message}`);
            if (!currentPath) alert(`处理根目录 "${dirHandle.name}" 时出错: ${error.message}`);
        }
        return fileInfos;
    };

    const generateNewFileName = (originalNameWithExt: string, currentNumberForFile: number): string => {
        const { namePart: originalNameWithoutExt, extPart: originalExt } = getNameAndExtension(originalNameWithExt);
        const currentFormData = formData();

        let newNameCoreBuilder = "";

        // 1. Add prefix (if any)
        if (currentFormData.prefix) {
            newNameCoreBuilder += currentFormData.prefix;
        }

        // 2. Add the original name part (without its extension)
        // This is the key change to make prefix/suffix/number additive.
        newNameCoreBuilder += originalNameWithoutExt;

        // 3. Add numbering (if any)
        if (currentFormData.startNumber !== null) {
            newNameCoreBuilder += currentNumberForFile;
        }

        // 4. Add suffix (if any)
        if (currentFormData.suffix) {
            newNameCoreBuilder += currentFormData.suffix;
        }
        
        // Edge case: If originalNameWithoutExt was empty (e.g. for a file like ".extonly")
        // and no prefix, number, or suffix were provided, newNameCoreBuilder would be empty.
        // However, originalNameWithoutExt from getNameAndExtension will not be empty if originalNameWithExt is just ".extonly"
        // For ".extonly", namePart is ".extonly", extPart is "".
        // If originalNameWithExt is "file." namePart is "file.", extPart is "".
        // If originalNameWithExt is ".file.ext", namePart is ".file", extPart is "ext".
        // So, originalNameWithoutExt should generally not be empty unless the original filename itself was truly empty or just an extension.
        // If newNameCoreBuilder happens to be empty (e.g. original file was just ".txt", and user wants to remove extension and add no prefix/suffix/number),
        // this is handled by the final return logic.

        // Determine the final extension
        let finalExtension = originalExt;
        if (currentFormData.extension) { // If user is targeting a specific extension (e.g., "jpg")
            // Check if the original file's extension matches the target (case-insensitive)
            if (originalExt.toLowerCase() === currentFormData.extension.toLowerCase().replace(/^\./, '')) {
                // If it matches, use the newExtension. If newExtension is an empty string, it removes the extension.
                finalExtension = currentFormData.newExtension.replace(/^\./, '');
            }
            // If the original extension doesn't match the target, finalExtension remains originalExt (no change to extension for this file).
        } else if (currentFormData.newExtension || currentFormData.newExtension === '') { 
            // If no specific target extension is set, but a newExtension IS provided (even if empty string meaning remove extension),
            // apply this newExtension to all files being processed.
            finalExtension = currentFormData.newExtension.replace(/^\./, '');
        }
        
        // Clean up any leading dot from user-inputted newExtension, as the dot is added by the template literal.
        finalExtension = finalExtension.replace(/^\./, '');

        // Construct the final name
        if (finalExtension) {
            return `${newNameCoreBuilder}.${finalExtension}`;
        } else {
            // If there's no final extension (either removed or never existed and none added), return just the core name.
            return newNameCoreBuilder;
        }
    };

    const generatePreview = (fileInfosToList: FileInfo[]): void => {
        const results: { original: string; new: string }[] = [];
        const currentFormData = formData();
        let numberCounter = currentFormData.startNumber !== null ? currentFormData.startNumber : 1;

        fileInfosToList.forEach(file => {
            const newNameOnly = generateNewFileName(file.name, numberCounter);
            const pathSegments = file.path.split('/');
            pathSegments.pop();
            const basePath = pathSegments.join('/');
            const fullNewPath = basePath ? `${basePath}/${newNameOnly}` : newNameOnly;

            results.push({
                original: file.path,
                new: fullNewPath
            });

            if (currentFormData.startNumber !== null) {
                numberCounter++;
            }
        });
        setPreviewResults(results);
    };

    const getNewFullPath = (originalFileInfoPath: string, newNameOnly: string): string => {
        const pathSegments = originalFileInfoPath.split('/');
        pathSegments.pop();
        const basePath = pathSegments.join('/');
        return basePath ? `${basePath}/${newNameOnly}` : newNameOnly;
    };

    // Internal helper for checking file existence
    const checkTargetFileExistenceInternal = async (
        parentDir: FileSystemDirectoryHandle,
        targetName: string,
        originalPathForError: string
    ): Promise<RenameResult> => {
        const targetFullPath = getNewFullPath(originalPathForError, targetName); // originalPathForError is just to construct path base
        try {
            await parentDir.getFileHandle(targetName);
            return {
                success: false,
                originalPath: originalPathForError,
                newPath: targetFullPath,
                error: new Error(`目标文件 "${targetName}" 已存在于目录 "${parentDir.name}" 中。`)
            };
        } catch (e: any) {
            if (e.name === 'NotFoundError') {
                return { success: true, originalPath: originalPathForError, newPath: targetFullPath };
            }
            return {
                success: false,
                originalPath: originalPathForError,
                newPath: targetFullPath,
                error: new Error(`检查文件 "${targetName}" 是否存在时出错: ${e.message || String(e)}`)
            };
        }
    };

    // Internal helper for copying content
    const copyFileContentInternal = async (
        sourceFileHandle: FileSystemFileHandle,
        targetParentDir: FileSystemDirectoryHandle,
        targetName: string,
        originalPathForError: string // The ultimate original path for consistent error reporting
    ): Promise<RenameResult> => {
        const targetFullPath = getNewFullPath(originalPathForError, targetName);
        let newFileHandle: FileSystemFileHandle | null = null;
        let writable: FileSystemWritableFileStream | null = null;

        try {
            const originalFile = await sourceFileHandle.getFile();
            const content = await originalFile.arrayBuffer();

            newFileHandle = await targetParentDir.getFileHandle(targetName, { create: true });
            writable = await newFileHandle.createWritable();
            await writable.write(content);
            await writable.close();
            writable = null;

            const newFile = await newFileHandle.getFile();
            if (newFile.size !== originalFile.size) {
                try { await targetParentDir.removeEntry(targetName); } catch { /* ignore */ }
                return {
                    success: false,
                    originalPath: originalPathForError,
                    newPath: targetFullPath,
                    error: new Error(`新文件 "${targetName}" 验证失败：文件大小不匹配。`)
                };
            }
            return { success: true, originalPath: originalPathForError, newPath: targetFullPath };
        } catch (error: any) {
            if (writable) {
                try { await writable.abort(); } catch { /* ignore */ }
            }
            return {
                success: false,
                originalPath: originalPathForError,
                newPath: targetFullPath,
                error: new Error(`写入新文件 "${targetName}" 失败: ${error.message || String(error)}`)
            };
        }
    };

    // Internal helper for removing a file
    const removeFileInternal = async (
        parentDir: FileSystemDirectoryHandle,
        fileNameToRemove: string,
        originalPathForError: string
    ): Promise<RenameResult> => {
        try {
            await parentDir.removeEntry(fileNameToRemove);
            return { success: true, originalPath: originalPathForError };
        } catch (error: any) {
            return {
                success: false,
                originalPath: originalPathForError,
                error: new Error(`删除文件 "${fileNameToRemove}" 失败: ${error.message || String(error)}`)
            };
        }
    };

    // Core rename logic for a single step (originalFile -> newNameOnly)
    // This implements the robust "copy-then-conditionally-delete"
    const renameFileInternal = async (
        currentFileInfo: FileInfo, // The file to be renamed in this step
        targetNameOnly: string,      // The new name for currentFileInfo in this step
        operationAllowsOverwrite: boolean // Whether this specific operation step allows overwrite
    ): Promise<RenameResult> => {
        // originalPathForReporting is the path of the very first file in a multi-stage rename,
        // or currentFileInfo.path if it's a single stage. We need to pass this down.
        // For simplicity in this refactor, we'll use currentFileInfo.path,
        // and the calling function (renameFile) can amend error messages if needed.

        if (currentFileInfo.name === targetNameOnly) {
            return {
                success: true,
                originalPath: currentFileInfo.path, // Path of the file input to *this* step
                newPath: getNewFullPath(currentFileInfo.path, targetNameOnly),
                skipped: true
            };
        }

        if (!operationAllowsOverwrite) {
            const existenceResult = await checkTargetFileExistenceInternal(
                currentFileInfo.parentDir,
                targetNameOnly,
                currentFileInfo.path
            );
            if (!existenceResult.success) {
                return existenceResult;
            }
        }

        const copyResult = await copyFileContentInternal(
            currentFileInfo.handle,
            currentFileInfo.parentDir,
            targetNameOnly,
            currentFileInfo.path // Reporting path for this step
        );
        if (!copyResult.success) {
            return copyResult;
        }

        let newFileHandleAfterCopy: FileSystemFileHandle;
        let actualNewFileName: string;
        try {
            newFileHandleAfterCopy = await currentFileInfo.parentDir.getFileHandle(targetNameOnly);
            actualNewFileName = (await newFileHandleAfterCopy.getFile()).name;
        } catch (e: any) {
            return {
                success: false,
                originalPath: currentFileInfo.path,
                newPath: getNewFullPath(currentFileInfo.path, targetNameOnly),
                error: new Error(`新文件 "${targetNameOnly}" 已创建但随后无法访问: ${(e as Error).message}. 原文件 (${currentFileInfo.name}) 可能未删除.`)
            };
        }

        const newFullPathWithActualName = getNewFullPath(currentFileInfo.path, actualNewFileName);
        const isSameUnderlyingFile = await currentFileInfo.handle.isSameEntry(newFileHandleAfterCopy);

        console.log(`  Internal Rename Step: "${currentFileInfo.name}" -> "${targetNameOnly}" (actual: "${actualNewFileName}")`);
        console.log(`    isSameEntry: ${isSameUnderlyingFile}`);

        if (isSameUnderlyingFile) {
            console.log(`    Action: Handles point to the same file. Source file ("${currentFileInfo.name}") will NOT be deleted.`);
        } else {
            console.log(`    Action: Handles point to different files. Source file ("${currentFileInfo.name}") WILL be deleted.`);
            const removeResult = await removeFileInternal(
                currentFileInfo.parentDir,
                currentFileInfo.name,
                currentFileInfo.path
            );
            if (!removeResult.success) {
                return {
                    success: false,
                    originalPath: currentFileInfo.path,
                    newPath: newFullPathWithActualName,
                    error: new Error(`新文件 "${actualNewFileName}" 已成功创建, 但删除原文件 "${currentFileInfo.name}" 失败: ${removeResult.error?.message}.`)
                };
            }
        }
        return { success: true, originalPath: currentFileInfo.path, newPath: newFullPathWithActualName };
    };

    // Main rename function called by handleSubmit
    // Main rename function called by handleSubmit
    const renameFile = async (fileInfo: FileInfo, newNameOnly: string): Promise<RenameResult> => {
        const originalName = fileInfo.name;
        const { namePart: originalBaseName, extPart: originalExt } = getNameAndExtension(originalName);
        const { namePart: newBaseName, extPart: newExt } = getNameAndExtension(newNameOnly);

        // Condition for special 2-stage handling:
        // 1. Base names are the same (case-sensitive comparison is fine here).
        // 2. Extensions are present on both original and new names.
        // 3. Extensions are case-variants of each other (lowercase same, actual different).
        const isExtensionCaseChangeOnly =
            originalBaseName === newBaseName &&
            originalExt !== '' && newExt !== '' &&
            originalExt.toLowerCase() === newExt.toLowerCase() &&
            originalExt !== newExt;

        if (isExtensionCaseChangeOnly) {
            console.log(`Performing 2-stage rename for extension case change: "${originalName}" to "${newNameOnly}"`);

            // Use a safer temporary file suffix.
            // Avoid characters like '~', ':', '*', '?', '"', '<', '>', '|' which can be problematic.
            const tempSuffix = "_renaming_temp_";
            let tempName = originalBaseName + "." + originalExt + tempSuffix;

            // Defensive check: if the generated tempName is somehow the same as the final newNameOnly,
            // or if the original file ALREADY ended with our tempSuffix (extremely unlikely but good to cover).
            if (tempName === newNameOnly || originalName.endsWith(tempSuffix)) {
                // Use an alternative temporary name if there's a conflict.
                tempName = originalBaseName + "." + originalExt + "_renaming_temp_alt_";
                console.warn(`Initial temp name ("${originalBaseName}.${originalExt}${tempSuffix}") conflicted. Using alternative: "${tempName}"`);
            }

            // Stage 1: Rename originalName to tempName
            // For this stage, we *must* allow overwrite if tempName somehow exists.
            console.log(`  Stage 1: Renaming "${originalName}" to temporary "${tempName}"`);
            const stage1Result = await renameFileInternal(fileInfo, tempName, true);

            if (!stage1Result.success || !stage1Result.newPath) {
                return {
                    success: false,
                    originalPath: fileInfo.path,
                    newPath: stage1Result.newPath, // May be undefined if copy didn't even start
                    error: new Error(`Stage 1 (rename "${originalName}" to temp "${tempName}") failed: ${stage1Result.error?.message || 'Unknown error'}.`)
                };
            }

            // Original file's content is now effectively in a file named by tempName.
            // We need a FileInfo object for this temporary file to pass to the next stage.
            let tempFileHandle;
            let actualTempNameOnDisk;
            try {
                // stage1Result.newPath contains the full relative path to the temp file.
                // Extract the actual name of the temp file as it exists on disk.
                actualTempNameOnDisk = stage1Result.newPath.split('/').pop();
                if (!actualTempNameOnDisk) { // Should not happen if newPath is valid
                    throw new Error("Could not derive temporary file name from path after stage 1.");
                }
                tempFileHandle = await fileInfo.parentDir.getFileHandle(actualTempNameOnDisk);
            } catch (e: any) {
                return {
                    success: false,
                    originalPath: fileInfo.path,
                    error: new Error(`Failed to get handle for temp file ("${actualTempNameOnDisk || tempName}") after stage 1: ${e.message}. Original file "${originalName}" status is uncertain (may be renamed or deleted).`)
                };
            }

            const tempFileInfo: FileInfo = {
                path: stage1Result.newPath,    // Full relative path to the temp file
                name: actualTempNameOnDisk,    // Actual name of the temp file on disk
                handle: tempFileHandle,
                parentDir: fileInfo.parentDir
            };

            // Stage 2: Rename tempFileInfo (the temporary file) to newNameOnly (the final desired name)
            // Use the global allowOverwrite() setting for this final step.
            console.log(`  Stage 2: Renaming temporary "${tempFileInfo.name}" to final "${newNameOnly}"`);
            const stage2Result = await renameFileInternal(tempFileInfo, newNameOnly, allowOverwrite());

            if (!stage2Result.success) {
                // If stage 2 fails, the file is left with the temporary name.
                return {
                    success: false,
                    originalPath: fileInfo.path, // Report initial original path in failure
                    newPath: stage2Result.newPath, // Path of the temp file or attempted final file
                    error: new Error(`Stage 2 (rename temp "${tempFileInfo.name}" to final "${newNameOnly}") failed: ${stage2Result.error?.message || 'Unknown error'}. File may be left as "${tempFileInfo.path}".`)
                };
            }

            // Both stages successful.
            return { success: true, originalPath: fileInfo.path, newPath: stage2Result.newPath };

        } else {
            // Use the standard single-stage rename for all other cases (not just extension case change).
            console.log(`Performing standard 1-stage rename for "${originalName}" to "${newNameOnly}"`);
            return renameFileInternal(fileInfo, newNameOnly, allowOverwrite());
        }
    };

    const handleFileSelect = async () => {
        try {
            const dirHandle = await window.showDirectoryPicker();
            setRootDirectoryHandle(dirHandle);
            setProcessing(true);
            const fileInfos = await processDirectory(dirHandle, '');
            setFiles(fileInfos);
            if (fileInfos.length === 0 && !includeSubfolders()) {
                alert("选择的目录是空的，或者您没有选择“包含子目录文件”而文件都在子目录中。");
            } else if (fileInfos.length === 0) {
                alert("未找到文件。");
            }
        } catch (error) {
            if (error instanceof DOMException && error.name === 'AbortError') {
                console.log('目录选择被用户取消');
            } else {
                console.error('选择目录时出错:', error);
                alert(`选择目录失败: ${(error as Error).message || '未知错误'}`);
            }
        } finally {
            setProcessing(false);
        }
    };

    const handleSubmit = async (e: Event) => {
        e.preventDefault();
        if (files().length === 0 || !rootDirectoryHandle()) {
            alert("请先选择一个目录并确保其中有文件。");
            return;
        }

        setProcessing(true);
        const currentFiles = files();
        const results: RenameResult[] = [];
        const formState = formData();
        let currentNumber = formState.startNumber !== null ? formState.startNumber : 1;

        for (const fileInfo of currentFiles) {
            const newNameOnly = generateNewFileName(fileInfo.name, currentNumber);

            if (newNameOnly === fileInfo.name) {
                results.push({ success: true, originalPath: fileInfo.path, newPath: getNewFullPath(fileInfo.path, newNameOnly), skipped: true });
                if (formState.startNumber !== null) currentNumber++;
                continue;
            }

            if (!newNameOnly || newNameOnly.includes('/') || newNameOnly.includes('\\')) {
                results.push({
                    success: false,
                    originalPath: fileInfo.path,
                    error: new Error(`生成的新文件名 "${newNameOnly}" 无效 (可能为空或包含路径分隔符)。`)
                });
                if (formState.startNumber !== null) currentNumber++;
                continue;
            }

            const result = await renameFile(fileInfo, newNameOnly);
            results.push(result);

            if (formState.startNumber !== null) {
                currentNumber++;
            }
        }

        const successCount = results.filter(r => r.success && !r.skipped).length;
        const skippedCount = results.filter(r => r.skipped).length;
        const failedCount = results.filter(r => !r.success).length;

        let message = `重命名操作完成！\n`;
        message += `成功重命名: ${successCount} 个文件\n`;
        if (skippedCount > 0) {
            message += `跳过 (名称未变): ${skippedCount} 个文件\n`;
        }
        if (failedCount > 0) {
            message += `失败: ${failedCount} 个文件\n\n`;
            message += `失败详情:\n`;
            results.filter(r => !r.success).forEach(fail => {
                message += `- "${fail.originalPath}" ${fail.newPath ? `-> "${fail.newPath}"` : ""}\n  原因: ${fail.error?.message || '未知错误'}\n`;
            });
        }
        alert(message);

        const rootHandle = rootDirectoryHandle();
        if (rootHandle) {
            try {
                setProcessing(true);
                const updatedFileInfos = await processDirectory(rootHandle, '');
                setFiles(updatedFileInfos);
            } catch (error) {
                console.error('刷新目录列表失败:', error);
                alert('目录列表刷新失败。您可能需要重新选择目录。');
                setFiles([]);
            } finally {
                setProcessing(false);
            }
        } else {
            setFiles([]);
            setProcessing(false);
        }
    };

    createEffect(() => {
        formData();
        const currentFiles = files();
        if (currentFiles.length > 0) {
            generatePreview(currentFiles);
        } else {
            setPreviewResults([]);
        }
    });

    createEffect(async (prevChecked) => {
        const currentChecked = includeSubfolders();
        const rootHandle = rootDirectoryHandle();
        if (rootHandle && prevChecked !== undefined && prevChecked !== currentChecked) {
            setProcessing(true);
            try {
                const fileInfos = await processDirectory(rootHandle, '');
                setFiles(fileInfos);
                if (fileInfos.length === 0) {
                    alert("更改“包含子目录文件”选项后，未找到文件。");
                }
            } catch (error) {
                console.error("Error reprocessing directory after subfolder toggle:", error);
                alert("重新处理目录时出错。");
                setFiles([]);
            } finally {
                setProcessing(false);
            }
        }
        return currentChecked;
    });


    return (
        <div class="p-4 bg-gray-100 min-h-screen">
            <div class="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md">
                <h1 class="text-3xl font-bold mb-6 text-gray-700">批量文件重命名</h1>

                <form onSubmit={handleSubmit} class="space-y-6 mb-8">
                    <div class="p-4 border rounded-md bg-gray-50">
                        <button
                            type="button"
                            class="btn btn-primary w-full sm:w-auto"
                            onClick={handleFileSelect}
                            disabled={processing()}
                        >
                            {processing() && !rootDirectoryHandle() ? '选择中...' : (rootDirectoryHandle() && processing() && files().length === 0 ? '扫描中...' : '选择主目录')}
                        </button>
                        <div class="mt-3 form-control">
                            <label class="cursor-pointer label justify-start gap-2">
                                <input
                                    type="checkbox"
                                    class="checkbox checkbox-primary"
                                    checked={includeSubfolders()}
                                    onChange={(e) => setIncludeSubfolders(e.currentTarget.checked)}
                                    disabled={processing() || !rootDirectoryHandle()}
                                />
                                <span class="label-text">包含子目录文件</span>
                            </label>
                            <label class="cursor-pointer label justify-start gap-2">
                                <input
                                    type="checkbox"
                                    class="checkbox checkbox-accent"
                                    checked={allowOverwrite()}
                                    onChange={(e) => setAllowOverwrite(e.currentTarget.checked)}
                                    disabled={processing() || !rootDirectoryHandle()}
                                />
                                <span class="label-text">允许覆盖同名文件</span>
                            </label>
                        </div>
                        {rootDirectoryHandle() && <p class="text-sm text-gray-500 mt-2">已选择目录：{rootDirectoryHandle()!.name}</p>}
                        {files().length > 0 && <p class="text-sm text-gray-500 mt-1">已加载 {files().length} 个文件进行处理。</p>}
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 p-4 border rounded-md bg-gray-50">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">文件名前缀</span>
                            </label>
                            <input
                                type="text"
                                class="input input-bordered w-full"
                                value={formData().prefix}
                                onInput={(e) => setFormData({ ...formData(), prefix: e.currentTarget.value })}
                                placeholder="例如: IMG_ (可选)"
                                disabled={processing()}
                            />
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">文件名后缀</span>
                            </label>
                            <input
                                type="text"
                                class="input input-bordered w-full"
                                value={formData().suffix}
                                onInput={(e) => setFormData({ ...formData(), suffix: e.currentTarget.value })}
                                placeholder="例如: _backup (可选)"
                                disabled={processing()}
                            />
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">起始编号</span>
                            </label>
                            <input
                                type="number"
                                class="input input-bordered w-full"
                                value={formData().startNumber === null ? '' : String(formData().startNumber)}
                                min="0"
                                onInput={(e) => {
                                    const value = e.currentTarget.value;
                                    setFormData({ ...formData(), startNumber: value === '' ? null : parseInt(value, 10) });
                                }}
                                placeholder="例如: 1 (可选, 留空不编号)"
                                disabled={processing()}
                            />
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">目标扩展名 (仅修改此类文件)</span>
                            </label>
                            <input
                                type="text"
                                class="input input-bordered w-full"
                                value={formData().extension}
                                onInput={(e) => setFormData({ ...formData(), extension: e.currentTarget.value.replace(/^\./, '') })}
                                placeholder="例如: jpg (可选, 留空则影响所有文件)"
                                disabled={processing()}
                            />
                        </div>

                        <div class="form-control md:col-span-2">
                            <label class="label">
                                <span class="label-text font-medium">新扩展名 (替换目标或所有)</span>
                            </label>
                            <input
                                type="text"
                                class="input input-bordered w-full"
                                value={formData().newExtension}
                                onInput={(e) => setFormData({ ...formData(), newExtension: e.currentTarget.value.replace(/^\./, '') })}
                                placeholder="例如: png (可选)"
                                disabled={processing()}
                            />
                        </div>
                    </div>

                    <button
                        type="submit"
                        class="btn btn-success w-full sm:w-auto"
                        disabled={files().length === 0 || processing()}
                    >
                        {processing() ? (
                            <>
                                <span class="loading loading-spinner loading-xs"></span>
                                处理中...
                            </>
                        ) : `确认重命名 ${previewResults().filter(r => r.original !== r.new).length} / ${files().length} 个文件`}
                    </button>
                </form>

                {previewResults().length > 0 && !processing() && (
                    <div class="mt-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">
                            预览结果 ({previewResults().filter(r => r.original !== r.new).length} 个文件将被实际更改)
                        </h2>
                        <div class="overflow-x-auto max-h-96 border rounded-md">
                            <table class="table table-zebra w-full table-sm sm:table-md">
                                <thead class="sticky top-0 bg-gray-200 z-10">
                                    <tr>
                                        <th class="px-3 py-2">原文件路径</th>
                                        <th class="px-3 py-2">新文件路径</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {previewResults().map(result => (
                                        <tr classList={{ 'text-gray-400': result.original === result.new }}>
                                            <td class="break-all px-3 py-2 text-xs sm:text-sm">{result.original}</td>
                                            <td classList={{
                                                'break-all': true,
                                                'px-3': true, 'py-2': true,
                                                'text-xs': true, 'sm:text-sm': true,
                                                'font-medium': result.original !== result.new,
                                                'text-green-600': result.original !== result.new
                                            }}>{result.new}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                )}
                {processing() && files().length > 0 && (
                    <div class="text-center p-4">
                        <span class="loading loading-lg loading-dots"></span>
                        <p>正在处理文件，请稍候...</p>
                    </div>
                )}
                {!rootDirectoryHandle() && !processing() && (
                    <p class="text-center text-gray-500 mt-8">请选择一个目录开始操作。</p>
                )}
                {rootDirectoryHandle() && files().length === 0 && !processing() && (
                    <p class="text-center text-gray-500 mt-8">当前目录为空或未选择包含子目录。请检查设置或选择其他目录。</p>
                )}
            </div>
        </div>
    );
}