import { createSignal, For } from 'solid-js';

interface ProcessResult {
  fileName: string;
  success: boolean;
  originalSize?: number;
  modifiedSize?: number;
  content?: string;
  error?: string;
  type?: 'xml' | 'rename';
  newFileName?: string;
}

interface ExtractedInfo {
  publisherId: string;
  fileName: string;
}

interface FileInfo {
  path: string;
  name: string;
  handle: FileSystemFileHandle;
  parentDir: FileSystemDirectoryHandle;
}

const XmlBatchModifier = () => {
  const [selectedFiles, setSelectedFiles] = createSignal<FileInfo[]>([]);
  const [articleType, setArticleType] = createSignal('open-access');
  const [isProcessing, setIsProcessing] = createSignal(false);
  const [processResults, setProcessResults] = createSignal<ProcessResult[]>([]);
  const [extractedInfo, setExtractedInfo] = createSignal<ExtractedInfo | null>(null);
  const [rootDirectoryHandle, setRootDirectoryHandle] = createSignal<FileSystemDirectoryHandle | null>(null);

  // 进度相关状态
  const [currentProcessingFile, setCurrentProcessingFile] = createSignal<string>('');
  const [processedCount, setProcessedCount] = createSignal(0);
  const [totalCount, setTotalCount] = createSignal(0);
  const [progressPercentage, setProgressPercentage] = createSignal(0);

  // 文章类型选项
  const articleTypes = [
    { value: 'open-access', label: 'Open Access' }
  ];

  // 处理目录选择 - 使用File System Access API
  const handleDirectorySelect = async () => {
    try {
      const dirHandle = await window.showDirectoryPicker();
      setRootDirectoryHandle(dirHandle);
      setIsProcessing(true);
      const fileInfos = await processDirectory(dirHandle, '');
      setSelectedFiles(fileInfos);
      if (fileInfos.length === 0) {
        alert("选择的目录中没有找到支持的文件类型。");
      }
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log('目录选择被用户取消');
      } else {
        console.error('选择目录时出错:', error);
        alert(`选择目录失败: ${(error as Error).message || '未知错误'}`);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理目录中的文件
  const processDirectory = async (dirHandle: FileSystemDirectoryHandle, currentPath = ''): Promise<FileInfo[]> => {
    const fileInfos: FileInfo[] = [];
    try {
      for await (const entry of dirHandle.values()) {
        const entryPath = currentPath ? `${currentPath}/${entry.name}` : entry.name;
        if (entry.kind === 'file') {
          const ext = entry.name.toLowerCase();
          // 支持的文件类型
          if (ext.endsWith('.xml') || ext.endsWith('.pdf') ||
            ext.endsWith('.mp4') || ext.endsWith('.jpg') ||
            ext.endsWith('.jpeg') || ext.endsWith('.png') ||
            ext.endsWith('.doc') || ext.endsWith('.docx') ||
            ext.endsWith('.txt') || ext.endsWith('.zip')) {
            fileInfos.push({
              path: entryPath,
              name: entry.name,
              handle: entry as FileSystemFileHandle,
              parentDir: dirHandle
            });
          }
        } else if (entry.kind === 'directory') {
          // 递归处理子目录
          try {
            const subDirHandle = await dirHandle.getDirectoryHandle(entry.name);
            const subFiles = await processDirectory(subDirHandle, entryPath);
            fileInfos.push(...subFiles);
          } catch (subDirError: any) {
            console.warn(`无法访问子目录 "${entryPath}": ${subDirError.message}. 跳过该目录.`);
          }
        }
      }
    } catch (error: any) {
      console.error(`处理目录 "${currentPath || dirHandle.name}" 时出错: ${error.message}`);
    }
    return fileInfos;
  };

  // 从XML中提取信息
  const extractInfoFromXml = (xmlContent: string): ExtractedInfo | null => {
    // 提取 publisher-id
    const publisherIdMatch = xmlContent.match(/<article-id pub-id-type="publisher-id">(.*?)<\/article-id>/);

    // 提取 DOI 中的文件名部分
    const doiMatch = xmlContent.match(/<article-id pub-id-type="doi">10\.21037\/(.*?)<\/article-id>/);

    if (publisherIdMatch && doiMatch) {
      return {
        publisherId: publisherIdMatch[1],
        fileName: doiMatch[1]
      };
    }

    return null;
  };

  // Open Access 类型的XML修改逻辑
  const processOpenAccessXml = (xmlContent: string, info: ExtractedInfo): string => {
    let modifiedContent = xmlContent;


    // 2.1 修改XML声明和DOCTYPE
    modifiedContent = modifiedContent.replace(
      /<\?xml version="1\.0" encoding="utf-8"\?>\s*<!DOCTYPE article PUBLIC "-\/\/NLM\/\/DTD Journal Publishing DTD v3\.0 20080202\/\/EN" "journalpublishing3\.dtd">/g,
      '<?xml version="1.0" encoding="UTF-8" standalone="no"?><!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd">'
    );

    // 2.2 修改article标签属性
    modifiedContent = modifiedContent.replace(
      /dtd-version="3\.0" xml:lang="en" xmlns:xlink="http:\/\/www\.w3\.org\/1999\/xlink" xmlns:mml="http:\/\/www\.w3\.org\/1998\/Math\/MathML"/g,
      'dtd-version="1.2" xml:lang="en" xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"'
    );

    // 2.3 在permissions标签中插入license信息
    const licenseContent = `<license xlink:href="http://creativecommons.org/licenses/by-nc-nd/4.0/"><license-p><italic>Open Access Statement:</italic> This is an Open Access article distributed in accordance with the Creative Commons Attribution-NonCommercial-NoDerivs 4.0 International License (CC BY-NC-ND 4.0), which permits the non-commercial replication and distribution of the article with the strict proviso that no changes or edits are made and the original work is properly cited (including links to both the formal publication through the relevant DOI and the license). See: <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by-nc-nd/4.0/">https://creativecommons.org/licenses/by-nc-nd/4.0</ext-link>.</license-p></license>`;
    // 2.3 在permissions标签末尾插入license信息
    modifiedContent = modifiedContent.replace(
      /<\/permissions>/g,
      `${licenseContent}</permissions>`
    );


    // // 2.3 删除journal-id标签
    // modifiedContent = modifiedContent.replace(
    //   /<journal-id journal-id-type="nlm-ta">.*?<\/journal-id>\s*/g,
    //   ''
    // );

    // // 2.4 修改abbrev-journal-title标签
    // modifiedContent = modifiedContent.replace(
    //   /<abbrev-journal-title abbrev-type="pubmed">(.*?)<\/abbrev-journal-title>/g,
    //   '<abbrev-journal-title>$1</abbrev-journal-title>'
    // );

    // // 2.5 删除equal-contrib属性
    // modifiedContent = modifiedContent.replace(
    //   /(<contrib contrib-type="author") equal-contrib="yes">/g,
    //   '$1>'
    // );

    // // 2.6 替换history标签为fn标签
    // modifiedContent = modifiedContent.replace(
    //   /<history>[\s\S]*?<\/history>/g,
    //   (match: string) => {
    //     // 尝试提取原始日期信息
    //     const receivedMatch = match.match(/<date date-type="received">[\s\S]*?<day>(\d+)<\/day>[\s\S]*?<month>(\d+)<\/month>[\s\S]*?<year>(\d+)<\/year>[\s\S]*?<\/date>/);
    //     const acceptedMatch = match.match(/<date date-type="accepted">[\s\S]*?<day>(\d+)<\/day>[\s\S]*?<month>(\d+)<\/month>[\s\S]*?<year>(\d+)<\/year>[\s\S]*?<\/date>/);

    //     let submittedDate = 'May 30, 2024';
    //     let acceptedDate = 'October 11, 2024';

    //     if (receivedMatch) {
    //       const months = ['January', 'February', 'March', 'April', 'May', 'June', 
    //                      'July', 'August', 'September', 'October', 'November', 'December'];
    //       submittedDate = `${months[parseInt(receivedMatch[2]) - 1]} ${parseInt(receivedMatch[1])}, ${receivedMatch[3]}`;
    //     }

    //     if (acceptedMatch) {
    //       const months = ['January', 'February', 'March', 'April', 'May', 'June', 
    //                      'July', 'August', 'September', 'October', 'November', 'December'];
    //       acceptedDate = `${months[parseInt(acceptedMatch[2]) - 1]} ${parseInt(acceptedMatch[1])}, ${acceptedMatch[3]}`;
    //     }

    //     return `<fn fn-type="other"><p><bold>Submitted:</bold> ${submittedDate}</p><p><bold>Accepted:</bold> ${acceptedDate}</p></fn>`;
    //   }
    // );

    // // 2.7 摘要小标题改为大写 - 只在abstract标签内处理
    // const abstractMatch = modifiedContent.match(/<abstract[\s\S]*?<\/abstract>/);
    // if (abstractMatch) {
    //   const abstractContent = abstractMatch[0];
    //   const modifiedAbstract = abstractContent.replace(
    //     /<title>([^<]+)<\/title>/g,
    //     (_match: string, title: string) => `<title>${title.toUpperCase()}</title>`
    //   );
    //   modifiedContent = modifiedContent.replace(abstractContent, modifiedAbstract);
    // }

    // 2.8 在</article-meta>前添加custom-meta-group
    modifiedContent = modifiedContent.replace(
      /<\/article-meta>/g,
      '<custom-meta-group><custom-meta><meta-name>OPEN-ACCESS</meta-name><meta-value>TRUE</meta-value></custom-meta></custom-meta-group></article-meta>'
    );

    // // 2.8 修改引用文献的rid属性（r改为bib）
    // modifiedContent = modifiedContent.replace(
    //   /rid="r(\d+)"/g,
    //   'rid="bib$1"'
    // );

    // // 修改文献列表的id属性
    // modifiedContent = modifiedContent.replace(
    //   /<ref id="r(\d+)">/g,
    //   '<ref id="bib$1">'
    // );

    // // 2.9 删除文献列表中的pmid和doi链接
    // modifiedContent = modifiedContent.replace(
    //   /<pub-id pub-id-type="pmid">.*?<\/pub-id>\s*/g,
    //   ''
    // );
    // modifiedContent = modifiedContent.replace(
    //   /<pub-id pub-id-type="doi">.*?<\/pub-id>\s*/g,
    //   ''
    // );

    // 使用提取的信息进行替换
    // 替换publisher-id中的内容为extractedFileName
    modifiedContent = modifiedContent.replace(
      /<article-id pub-id-type="publisher-id">.*?<\/article-id>/g,
      `<article-id pub-id-type="publisher-id">${info.fileName}</article-id>`
    );

    // 批量替换内容中的publisher-id为新的文件名
    const regex = new RegExp(info.publisherId, 'g');
    modifiedContent = modifiedContent.replace(regex, info.fileName);

    // footnote 加标题
    modifiedContent = modifiedContent.replace(
      /<fn-group>/g,
      '<sec><title>Footnote</title><fn-group>'
    );

    modifiedContent = modifiedContent.replace(
      /<\/fn-group>/g,
      '</fn-group></sec>'
    );

    // 视频media 移动到正文下方，添加标签
    const mediaMatches = modifiedContent.match(/<media[^>]*>[\s\S]*?(<\/media>)?/g);
    console.log(mediaMatches)
    if (mediaMatches) {
      // 先删除原有的media标签
      mediaMatches.forEach(media => {
        modifiedContent = modifiedContent.replace(media, '');
      });

      // 转换 media 标签
      let counter = 1;
      const transformedMedia = mediaMatches.map(media => {
        // 提取 href
        const hrefMatch = media.match(/xlink:href="([^"]+)"/);
        let href = hrefMatch ? hrefMatch[1] : '';

        // 补充 .mp4 后缀
        if (href && !href.endsWith('.mp4')) {
          href = href.replace(/\.*$/, '') + '.mp4';
        }

        return `<media id="s${counter++}" content-type="local-data" mimetype="video" xlink:href="${href}" mime-subtype="mp4"/>`;
      });


      // 在</body>和<back>之间添加media标签，包装在sec和supplementary-material中
       // 在</body>前插入
      const mediaSection = `<sec sec-type="supplementary-material">
      <supplementary-material>
      ${transformedMedia.join('\n')}
      </supplementary-material>
      </sec>`;
      modifiedContent = modifiedContent.replace(
        /<\/body>/,
        `</body>\n${mediaSection}`
      );
    }

    // related-article 删除多余属性，调整顺序
    modifiedContent = modifiedContent.replace(
      /<related-article[^>]*>/g,
      (match) => {
        // 提取必要的属性
        const typeMatch = match.match(/related-article-type="([^"]*)"/);
        const idMatch = match.match(/id="([^"]*)"/);
        const xlinkMatch = match.match(/xlink:href="([^"]*)"/);

        let newTag = '<related-article';
        if (idMatch) newTag += ` id="${idMatch[1]}"`;
        newTag += ` ext-link-type="doi"`
        if (typeMatch) newTag += ` related-article-type="${typeMatch[1]}"`;
        if (xlinkMatch) newTag += ` xlink:href="${xlinkMatch[1]}"`;
        newTag += '>';

        return newTag;
      }
    );
    // table-wrap-foot 标签处理
    modifiedContent = modifiedContent.replace(
      /<table-wrap-foot>([\s\S]*?)<\/table-wrap-foot>/g,
      (match, content) => {
        // 为每个p标签外面包装fn标签
        const wrappedContent = content.replace(
          /<p>([\s\S]*?)<\/p>/g,
          '<fn><p>$1</p></fn>'
        );
        return `<table-wrap-foot>${wrappedContent}</table-wrap-foot>`;
      }
    );

    // 清理多余的空行和空白
    modifiedContent = modifiedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

    return modifiedContent;
  };

  // 实际重命名文件
  const renameFileActual = async (fileInfo: FileInfo, newFileName: string): Promise<ProcessResult> => {
    if (fileInfo.name === newFileName) {
      return {
        fileName: fileInfo.name,
        success: true,
        newFileName: newFileName,
        type: 'rename'
      };
    }

    try {
      // 检查目标文件是否已存在
      try {
        await fileInfo.parentDir.getFileHandle(newFileName);
        return {
          fileName: fileInfo.name,
          success: false,
          error: `目标文件 "${newFileName}" 已存在`,
          type: 'rename'
        };
      } catch (e: any) {
        if (e.name !== 'NotFoundError') {
          throw e;
        }
      }

      // 读取原文件内容
      const originalFile = await fileInfo.handle.getFile();
      const content = await originalFile.arrayBuffer();

      // 创建新文件
      const newFileHandle = await fileInfo.parentDir.getFileHandle(newFileName, { create: true });
      const writable = await newFileHandle.createWritable();
      await writable.write(content);
      await writable.close();

      // 验证新文件
      const newFile = await newFileHandle.getFile();
      if (newFile.size !== originalFile.size) {
        await fileInfo.parentDir.removeEntry(newFileName);
        return {
          fileName: fileInfo.name,
          success: false,
          error: `新文件验证失败：文件大小不匹配`,
          type: 'rename'
        };
      }

      // 检查是否是同一个文件（某些文件系统可能会这样）
      const isSameFile = await fileInfo.handle.isSameEntry(newFileHandle);
      if (!isSameFile) {
        // 删除原文件
        await fileInfo.parentDir.removeEntry(fileInfo.name);
      }

      return {
        fileName: fileInfo.name,
        success: true,
        newFileName: newFileName,
        type: 'rename'
      };
    } catch (error: any) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: `重命名失败: ${error.message}`,
        type: 'rename'
      };
    }
  };

  // 处理文件重命名
  const processFileRename = async (fileInfo: FileInfo, info: ExtractedInfo): Promise<ProcessResult> => {
    if (fileInfo.name.includes(info.publisherId)) {
      const newFileName = fileInfo.name.replace(new RegExp(info.publisherId, 'g'), info.fileName);
      return await renameFileActual(fileInfo, newFileName);
    } else {
      return {
        fileName: fileInfo.name,
        success: false,
        error: `文件名中未找到 "${info.publisherId}"`,
        type: 'rename'
      };
    }
  };

  // 处理单个XML文件
  // 保存XML文件
  const saveXmlFile = async (fileInfo: FileInfo, content: string, newFileName: string, originalSize: number): Promise<ProcessResult> => {
    try {
      // 如果文件名没有变化，直接覆盖原文件
      if (fileInfo.name === newFileName) {
        const writable = await fileInfo.handle.createWritable();
        await writable.write(content);
        await writable.close();

        return {
          fileName: fileInfo.name,
          success: true,
          originalSize: originalSize,
          modifiedSize: content.length,
          content: content,
          newFileName: newFileName,
          type: 'xml'
        };
      } else {
        // 需要重命名，先保存新文件再删除旧文件
        try {
          await fileInfo.parentDir.getFileHandle(newFileName);
          return {
            fileName: fileInfo.name,
            success: false,
            error: `目标文件 "${newFileName}" 已存在`,
            type: 'xml'
          };
        } catch (e: any) {
          if (e.name !== 'NotFoundError') {
            throw e;
          }
        }

        // 创建新文件
        const newFileHandle = await fileInfo.parentDir.getFileHandle(newFileName, { create: true });
        const writable = await newFileHandle.createWritable();
        await writable.write(content);
        await writable.close();

        // 验证新文件
        const newFile = await newFileHandle.getFile();
        if (newFile.size !== content.length) {
          await fileInfo.parentDir.removeEntry(newFileName);
          return {
            fileName: fileInfo.name,
            success: false,
            error: `新文件验证失败：文件大小不匹配`,
            type: 'xml'
          };
        }

        // 检查是否是同一个文件
        const isSameFile = await fileInfo.handle.isSameEntry(newFileHandle);
        if (!isSameFile) {
          // 删除原文件
          await fileInfo.parentDir.removeEntry(fileInfo.name);
        }

        return {
          fileName: fileInfo.name,
          success: true,
          originalSize: originalSize,
          modifiedSize: content.length,
          content: content,
          newFileName: newFileName,
          type: 'xml'
        };
      }
    } catch (error: any) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: `保存XML文件失败: ${error.message}`,
        type: 'xml'
      };
    }
  };

  const processXmlFile = async (fileInfo: FileInfo, info: ExtractedInfo): Promise<ProcessResult> => {
    try {
      const file = await fileInfo.handle.getFile();
      const originalContent = await file.text();
      let modifiedContent = originalContent;

      if (articleType() === 'open-access') {
        modifiedContent = processOpenAccessXml(originalContent, info);
      }

      // 生成新的XML文件名
      const newXmlFileName = fileInfo.name.includes(info.publisherId)
        ? fileInfo.name.replace(new RegExp(info.publisherId, 'g'), info.fileName)
        : `${info.fileName}.xml`;

      // 保存文件
      return await saveXmlFile(fileInfo, modifiedContent, newXmlFileName, file.size);
    } catch (error) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: error instanceof Error ? error.message : '处理文件时发生错误',
        type: 'xml'
      };
    }
  };

  // 批量处理文件
  const handleBatchProcess = async () => {
    if (selectedFiles().length === 0) {
      alert('请先选择文件');
      return;
    }

    setIsProcessing(true);
    setProcessResults([]);

    // 找到所有XML文件
    const xmlFiles = selectedFiles().filter(f => f.name.toLowerCase().endsWith('.xml'));
    const otherFiles = selectedFiles().filter(f => !f.name.toLowerCase().endsWith('.xml'));

    if (xmlFiles.length === 0) {
      alert('请至少选择一个XML文件以提取Publisher ID和文件名信息');
      setIsProcessing(false);
      return;
    }

    try {
      const results: ProcessResult[] = [];
      const processedFiles = new Set<string>(); // 跟踪已处理的文件

      // 计算总文件数
      const totalFiles = selectedFiles().length;
      setTotalCount(totalFiles);
      setProcessedCount(0);
      setProgressPercentage(0);

      let currentIndex = 0;

      // 处理每个XML文件及其相关附件
      for (const xmlFile of xmlFiles) {
        setCurrentProcessingFile(`正在处理XML文件: ${xmlFile.name}`);

        // 从当前XML文件提取信息
        const file = await xmlFile.handle.getFile();
        const xmlContent = await file.text();
        const info = extractInfoFromXml(xmlContent);

        if (!info) {
          results.push({
            fileName: xmlFile.name,
            success: false,
            error: '无法从XML文件中提取Publisher ID和DOI信息',
            type: 'xml'
          });
          currentIndex++;
          setProcessedCount(currentIndex);
          setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));
          continue;
        }

        // 设置提取的信息（用于UI显示）
        if (!extractedInfo()) {
          setExtractedInfo(info);
        }

        // 处理当前XML文件
        const xmlResult = await processXmlFile(xmlFile, info);
        results.push(xmlResult);
        processedFiles.add(xmlFile.path);
        currentIndex++;
        setProcessedCount(currentIndex);
        setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));

        // 查找与当前XML相关的附件文件（包含相同的Publisher ID）
        const relatedFiles = otherFiles.filter(otherFile =>
          !processedFiles.has(otherFile.path) &&
          otherFile.name.includes(info.publisherId)
        );

        // 处理相关附件文件
        for (const relatedFile of relatedFiles) {
          setCurrentProcessingFile(`正在重命名附件: ${relatedFile.name}`);
          const renameResult = await processFileRename(relatedFile, info);
          results.push(renameResult);
          processedFiles.add(relatedFile.path);
          currentIndex++;
          setProcessedCount(currentIndex);
          setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));
        }
      }

      // 处理剩余的未匹配文件（显示为跳过）
      const remainingFiles = otherFiles.filter(f => !processedFiles.has(f.path));
      for (const remainingFile of remainingFiles) {
        setCurrentProcessingFile(`跳过未匹配文件: ${remainingFile.name}`);
        results.push({
          fileName: remainingFile.name,
          success: false,
          error: '未找到匹配的XML文件或Publisher ID',
          type: 'rename'
        });
        currentIndex++;
        setProcessedCount(currentIndex);
        setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));
      }

      setProcessResults(results);
      setCurrentProcessingFile('处理完成');

      // 显示处理结果
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;
      const xmlCount = results.filter(r => r.type === 'xml' && r.success).length;
      const renameCount = results.filter(r => r.type === 'rename' && r.success).length;

      let message = `处理完成！\n`;
      message += `成功处理: ${successCount} 个文件\n`;
      message += `  - XML文件: ${xmlCount} 个\n`;
      message += `  - 重命名文件: ${renameCount} 个\n`;
      if (failedCount > 0) {
        message += `失败: ${failedCount} 个文件\n`;
      }
      alert(message);

      // 重置进度状态
      setTimeout(() => {
        setCurrentProcessingFile('');
        setProcessedCount(0);
        setTotalCount(0);
        setProgressPercentage(0);
      }, 2000);

      setIsProcessing(false);
    } catch (error) {
      alert('处理文件时发生错误: ' + (error instanceof Error ? error.message : '未知错误'));
      setIsProcessing(false);
    }
  };



  return (
    <div class="p-6 max-w-6xl mx-auto">
      <div class="mb-6">
        <h1 class="text-3xl font-bold mb-2">批量XML文件修改工具（威科规范）</h1>
        <p class="text-gray-600 mb-4">自动从XML文件中提取Publisher ID和文件名信息，直接修改本地文件并重命名相关附件。</p>

        <div class="alert alert-info mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <h3 class="font-bold">功能说明</h3>
            <div class="text-sm">
              <p>• <strong>智能匹配：</strong>为每个XML文件分别提取Publisher ID和DOI文件名</p>
              <p>• <strong>XML处理：</strong>直接修改XML文件内容和文件名（Open Access格式转换）</p>
              <p>• <strong>附件重命名：</strong>自动找到并重命名与每个XML相关的附件文件</p>
              <p>• <strong>本地操作：</strong>直接修改本地文件，无需下载</p>
              <p>• <strong>支持格式：</strong>XML, PDF, MP4, JPG, JPEG, PNG, DOC, DOCX, TXT, ZIP</p>
            </div>
          </div>
        </div>

        <div class="alert alert-success">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h3 class="font-bold">使用说明</h3>
            <div class="text-sm">
              <p>1. 点击"选择目录"按钮，选择包含XML文件和相关附件的文件夹</p>
              <p>2. 系统会自动扫描目录中的所有支持文件</p>
              <p>3. 点击"开始批量处理"，系统会：</p>
              <p class="ml-4">• 为每个XML文件提取其Publisher ID和新文件名</p>
              <p class="ml-4">• 处理XML文件的格式转换和重命名</p>
              <p class="ml-4">• 找到包含相同Publisher ID的附件文件并重命名</p>
              <p>4. 处理完成后，所有文件都会直接在原位置被修改</p>
            </div>
          </div>
        </div>
      </div>

      {/* 文件选择区域 */}
      <div class="card bg-base-100 shadow-xl mb-6">
        <div class="card-body">
          <h2 class="card-title">文件选择</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">文章类型</span>
              </label>
              <select
                class="select select-bordered w-full"
                value={articleType()}
                onChange={(e) => setArticleType(e.target.value)}
              >
                <For each={articleTypes}>
                  {(type) => (
                    <option value={type.value}>{type.label}</option>
                  )}
                </For>
              </select>
            </div>

            {extractedInfo() && (
              <div class="form-control">
                <label class="label">
                  <span class="label-text">提取的信息</span>
                </label>
                <div class="bg-base-200 p-3 rounded">
                  <p class="text-sm"><strong>Publisher ID:</strong> {extractedInfo()!.publisherId}</p>
                  <p class="text-sm"><strong>新文件名:</strong> {extractedInfo()!.fileName}</p>
                </div>
              </div>
            )}
          </div>

          <div class="form-control">
            <button
              type="button"
              class="btn btn-primary w-full"
              onClick={handleDirectorySelect}
              disabled={isProcessing()}
            >
              {isProcessing() && !rootDirectoryHandle() ? '选择中...' : '选择目录（包含XML及其他文件）'}
            </button>
            {rootDirectoryHandle() && (
              <p class="text-sm text-gray-500 mt-2">
                已选择目录：{rootDirectoryHandle()!.name}
              </p>
            )}
          </div>

          {selectedFiles().length > 0 && (
            <div class="mt-4">
              <p class="text-sm text-gray-600 mb-2">
                已选择 {selectedFiles().length} 个文件
                ({selectedFiles().filter(f => f.name.toLowerCase().endsWith('.xml')).length} 个XML文件,
                {selectedFiles().filter(f => !f.name.toLowerCase().endsWith('.xml')).length} 个其他文件)
              </p>
              <div class="max-h-32 overflow-y-auto">
                <For each={selectedFiles()}>
                  {(file) => (
                    <div class="badge badge-outline mr-2 mb-2">{file.name}</div>
                  )}
                </For>
              </div>
            </div>
          )}

          <div class="card-actions justify-end mt-4">
            <button
              class="btn btn-primary"
              onClick={handleBatchProcess}
              disabled={isProcessing() || selectedFiles().length === 0}
            >
              {isProcessing() ? (
                <>
                  <span class="loading loading-spinner loading-sm"></span>
                  处理中...
                </>
              ) : (
                '开始批量处理'
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 进度显示区域 */}
      {isProcessing() && (
        <div class="card bg-base-100 shadow-xl mb-6">
          <div class="card-body">
            <h2 class="card-title">处理进度</h2>

            <div class="space-y-4">
              {/* 当前处理文件 */}
              <div class="text-sm text-gray-600">
                {currentProcessingFile()}
              </div>

              {/* 进度条 */}
              <div class="w-full">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>已处理: {processedCount()} / {totalCount()} 个文件</span>
                  <span>{progressPercentage()}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    style={`width: ${progressPercentage()}%`}
                  ></div>
                </div>
              </div>

              {/* 处理状态 */}
              <div class="flex items-center gap-2">
                <span class="loading loading-spinner loading-sm"></span>
                <span class="text-sm">正在处理文件，请稍候...</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 处理结果区域 */}
      {processResults().length > 0 && (
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <div class="flex justify-between items-center mb-4">
              <h2 class="card-title">处理结果</h2>
              <div class="text-sm text-gray-600">
                成功处理: {processResults().filter(r => r.success).length} / {processResults().length} 个文件
              </div>
            </div>

            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>文件名</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th>信息</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <For each={processResults()}>
                    {(result) => (
                      <tr>
                        <td class="font-mono text-sm">{result.fileName}</td>
                        <td>
                          <div class={`badge ${result.type === 'xml' ? 'badge-primary' : 'badge-secondary'}`}>
                            {result.type === 'xml' ? 'XML处理' : '文件重命名'}
                          </div>
                        </td>
                        <td>
                          {result.success ? (
                            <div class="badge badge-success">成功</div>
                          ) : (
                            <div class="badge badge-error">失败</div>
                          )}
                        </td>
                        <td class="text-sm">
                          {result.success ? (
                            result.type === 'xml' ?
                              `${result.originalSize} → ${result.modifiedSize} bytes` :
                              `新文件名: ${result.newFileName}`
                          ) : (
                            <span class="text-error">{result.error}</span>
                          )}
                        </td>
                        <td>
                          {result.success ? (
                            <span class="text-success text-sm">✓ 已完成</span>
                          ) : (
                            <span class="text-error text-sm">✗ 失败</span>
                          )}
                        </td>
                      </tr>
                    )}
                  </For>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default XmlBatchModifier;