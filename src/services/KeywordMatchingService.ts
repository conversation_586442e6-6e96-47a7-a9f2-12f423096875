/**
 * 关键词匹配算法服务
 * 提供基于关键词重合度和词义相似度的文章排序功能
 */

export class KeywordMatchingService {
  
  /**
   * 计算两个字符串的编辑距离（Levenshtein距离）
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }
    
    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 计算字符串相似度（基于编辑距离）
   */
  private static stringSimilarity(str1: string, str2: string): number {
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;
    
    const distance = this.levenshteinDistance(str1.toLowerCase(), str2.toLowerCase());
    return (maxLength - distance) / maxLength;
  }

  /**
   * 提取文本中的关键词（简单的词干提取）
   */
  private static extractKeywords(text: string): string[] {
    // 移除标点符号，转换为小写，分割单词
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2); // 过滤掉长度小于3的单词
    
    // 移除常见停用词
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
      'from', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among'
    ]);
    
    return words.filter(word => !stopWords.has(word));
  }

  /**
   * 计算关键词在文本中的匹配分数
   */
  private static calculateKeywordMatch(keyword: string, text: string): KeywordMatch {
    const normalizedKeyword = keyword.toLowerCase().trim();
    const normalizedText = text.toLowerCase();
    
    // 精确匹配
    if (normalizedText.includes(normalizedKeyword)) {
      return {
        keyword,
        score: 1.0,
        type: 'exact'
      };
    }
    
    // 部分匹配 - 检查关键词的各个部分
    const keywordParts = normalizedKeyword.split(/\s+/);
    let partialMatches = 0;
    
    for (const part of keywordParts) {
      if (normalizedText.includes(part)) {
        partialMatches++;
      }
    }
    
    if (partialMatches > 0) {
      const partialScore = partialMatches / keywordParts.length * 0.8;
      return {
        keyword,
        score: partialScore,
        type: 'partial'
      };
    }
    
    // 语义相似度匹配
    const textWords = this.extractKeywords(text);
    let maxSimilarity = 0;
    
    for (const word of textWords) {
      const similarity = this.stringSimilarity(normalizedKeyword, word);
      maxSimilarity = Math.max(maxSimilarity, similarity);
    }
    
    // 只有相似度超过阈值才认为是语义匹配
    if (maxSimilarity > 0.7) {
      return {
        keyword,
        score: maxSimilarity * 0.6,
        type: 'semantic'
      };
    }
    
    return {
      keyword,
      score: 0,
      type: 'semantic'
    };
  }

  /**
   * 计算文章的关键词匹配分数（基于命中率优先）
   */
  static calculateArticleScore(article: PubMedArticle, keywords: string[]): ScoredArticle {
    const keywordMatches: KeywordMatch[] = [];
    let hitCount = 0; // 命中的关键词数量

    // 构建用于匹配的文本内容
    const searchableText = [
      article.title,
      article.abstract || '',
      ...(article.keywords || []),
      article.journal,
      ...article.authors.map(author => `${author.forename} ${author.lastname}`),
      ...(article.affiliations || [])
    ].join(' ');

    // 计算每个关键词的匹配情况
    for (const keyword of keywords) {
      const match = this.calculateKeywordMatch(keyword, searchableText);
      keywordMatches.push(match);

      // 只要有匹配就算命中（分数大于0）
      if (match.score > 0) {
        hitCount++;
      }
    }

    // 计算命中率（命中的关键词数 / 总关键词数）
    const hitRate = keywords.length > 0 ? hitCount / keywords.length : 0;

    // 计算加权分数作为次要排序依据
    let weightedScore = 0;
    for (const match of keywordMatches) {
      if (match.score > 0) {
        let score = match.score;
        if (match.type === 'exact') {
          score *= 2.0; // 精确匹配权重最高
        } else if (match.type === 'partial') {
          score *= 1.5; // 部分匹配权重中等
        }
        weightedScore += score;
      }
    }

    // 标题匹配额外加分
    const titleMatches = keywords.filter(keyword =>
      article.title.toLowerCase().includes(keyword.toLowerCase())
    ).length;
    if (titleMatches > 0) {
      weightedScore += titleMatches * 0.5;
    }

    // 关键词字段匹配额外加分
    if (article.keywords) {
      const keywordFieldMatches = keywords.filter(keyword =>
        article.keywords!.some(articleKeyword =>
          articleKeyword.toLowerCase().includes(keyword.toLowerCase())
        )
      ).length;
      if (keywordFieldMatches > 0) {
        weightedScore += keywordFieldMatches * 0.3;
      }
    }

    // 最终相关性分数：命中率为主要因子，加权分数为次要因子
    // 使用命中率 * 100 + 归一化的加权分数，确保命中率优先
    const normalizedWeightedScore = Math.min(weightedScore / keywords.length, 1.0);
    const relevanceScore = hitRate * 100 + normalizedWeightedScore;

    return {
      ...article,
      relevanceScore,
      keywordMatches,
      hitRate, // 添加命中率字段用于显示
      hitCount // 添加命中数量字段用于显示
    };
  }

  /**
   * 对文章列表按相关性排序
   */
  static rankArticles(articles: PubMedArticle[], keywords: string[]): ScoredArticle[] {
    if (!keywords || keywords.length === 0) {
      return articles.map(article => ({
        ...article,
        relevanceScore: 0,
        keywordMatches: []
      }));
    }
    
    // 计算每篇文章的分数
    const scoredArticles = articles.map(article => 
      this.calculateArticleScore(article, keywords)
    );
    
    // 按相关性分数降序排序
    return scoredArticles.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 从文档内容中提取关键词
   */
  static extractKeywordsFromText(text: string, maxKeywords: number = 10): string[] {
    const words = this.extractKeywords(text);
    
    // 计算词频
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    });
    
    // 按频率排序并返回前N个关键词
    return Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxKeywords)
      .map(([word]) => word);
  }

  /**
   * 高亮显示匹配的关键词
   */
  static highlightKeywords(text: string, keywords: string[]): string {
    let highlightedText = text;
    
    keywords.forEach(keyword => {
      const regex = new RegExp(`(${keyword})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });
    
    return highlightedText;
  }
}
