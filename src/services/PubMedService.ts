/**
 * PubMed API 服务
 * 提供 PubMed 文献搜索和详细信息获取功能
 */

export class PubMedService {
  private static readonly BASE_URL = 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/';
  private static readonly SEARCH_URL = `${PubMedService.BASE_URL}esearch.fcgi`;
  private static readonly FETCH_URL = `${PubMedService.BASE_URL}efetch.fcgi`;
  private static readonly SUMMARY_URL = `${PubMedService.BASE_URL}esummary.fcgi`;

  /**
   * 搜索 PubMed 文献
   */
  static async searchArticles(params: PubMedSearchParams): Promise<PubMedSearchResult> {
    const searchParams = new URLSearchParams({
      db: 'pubmed',
      term: params.query,
      retmode: 'json',
      retmax: (params.maxResults || 9999).toString(), // 默认调到9999
      sort: params.sort === 'date' ? 'pub_date' : 'relevance'
    });

    try {
      // 使用 Chrome 扩展的 fetch API 避免 CORS 问题
      const response = await fetch(`${this.SEARCH_URL}?${searchParams}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
        }
      });

      if (!response.ok) {
        throw new Error(`PubMed search failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.esearchresult;
    } catch (error) {
      console.error('PubMed search error:', error);
      throw new Error('Failed to search PubMed articles');
    }
  }

  /**
   * 获取文章详细信息
   */
  static async getArticleDetails(pmids: string[]): Promise<PubMedDetailResponse> {
    const fetchParams = new URLSearchParams({
      db: 'pubmed',
      id: pmids.join(','),
      retmode: 'json',
      rettype: 'abstract'
    });

    try {
      const response = await fetch(`${this.SUMMARY_URL}?${fetchParams}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
        }
      });

      if (!response.ok) {
        throw new Error(`PubMed fetch failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('PubMed fetch error:', error);
      throw new Error('Failed to fetch article details');
    }
  }

  /**
   * 获取文章的完整信息（包括摘要）
   */
  static async getFullArticleInfo(pmids: string[]): Promise<string> {
    const fetchParams = new URLSearchParams({
      db: 'pubmed',
      id: pmids.join(','),
      retmode: 'xml',
      rettype: 'abstract'
    });

    try {
      const response = await fetch(`${this.FETCH_URL}?${fetchParams}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/xml',
          'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
        }
      });

      if (!response.ok) {
        throw new Error(`PubMed fetch failed: ${response.statusText}`);
      }

      return await response.text();
    } catch (error) {
      console.error('PubMed fetch error:', error);
      throw new Error('Failed to fetch full article info');
    }
  }

  /**
   * 解析 XML 响应并提取文章信息
   */
  static parseArticleXML(xmlText: string): PubMedArticle[] {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
    const articles: PubMedArticle[] = [];

    const pubmedArticles = xmlDoc.querySelectorAll('PubmedArticle');
    
    pubmedArticles.forEach(articleNode => {
      const pmid = articleNode.querySelector('PMID')?.textContent || '';
      const titleNode = articleNode.querySelector('ArticleTitle');
      const title = titleNode?.textContent || '';
      
      // 提取摘要
      const abstractNodes = articleNode.querySelectorAll('AbstractText');
      let abstract = '';
      abstractNodes.forEach(node => {
        if (abstract) abstract += ' ';
        abstract += node.textContent || '';
      });

      // 提取作者信息
      const authors: PubMedAuthor[] = [];
      const authorNodes = articleNode.querySelectorAll('Author');
      
      authorNodes.forEach(authorNode => {
        const lastname = authorNode.querySelector('LastName')?.textContent || '';
        const forename = authorNode.querySelector('ForeName')?.textContent || '';
        const initials = authorNode.querySelector('Initials')?.textContent || '';
        
        // 提取机构信息
        const affiliationNode = authorNode.querySelector('AffiliationInfo Affiliation');
        const affiliation = affiliationNode?.textContent || '';
        
        // 提取 ORCID（如果有）
        const orcidNode = authorNode.querySelector('Identifier[Source="ORCID"]');
        const orcid = orcidNode?.textContent || '';

        if (lastname || forename) {
          authors.push({
            lastname,
            forename,
            initials,
            affiliation,
            orcid: orcid || undefined
          });
        }
      });

      // 提取期刊信息
      const journalNode = articleNode.querySelector('Journal Title');
      const journal = journalNode?.textContent || '';

      // 提取发表日期
      const pubDateNode = articleNode.querySelector('PubDate');
      let pubdate = '';
      if (pubDateNode) {
        const year = pubDateNode.querySelector('Year')?.textContent || '';
        const month = pubDateNode.querySelector('Month')?.textContent || '';
        const day = pubDateNode.querySelector('Day')?.textContent || '';
        pubdate = [year, month, day].filter(Boolean).join('-');
      }

      // 提取 DOI
      const doiNode = articleNode.querySelector('ArticleId[IdType="doi"]');
      const doi = doiNode?.textContent || '';

      // 提取关键词
      const keywords: string[] = [];
      const keywordNodes = articleNode.querySelectorAll('Keyword');
      keywordNodes.forEach(node => {
        const keyword = node.textContent?.trim();
        if (keyword) keywords.push(keyword);
      });

      // 提取机构信息
      const affiliations: string[] = [];
      const affiliationNodes = articleNode.querySelectorAll('AffiliationInfo Affiliation');
      affiliationNodes.forEach(node => {
        const affiliation = node.textContent?.trim();
        if (affiliation && !affiliations.includes(affiliation)) {
          affiliations.push(affiliation);
        }
      });

      if (pmid && title) {
        articles.push({
          pmid,
          title,
          abstract: abstract || undefined,
          authors,
          journal,
          pubdate,
          doi: doi || undefined,
          keywords: keywords.length > 0 ? keywords : undefined,
          affiliations: affiliations.length > 0 ? affiliations : undefined
        });
      }
    });

    return articles;
  }

  /**
   * 搜索并获取完整的文章信息
   */
  static async searchAndFetchArticles(params: PubMedSearchParams): Promise<PubMedArticle[]> {
    try {
      // 首先搜索获取 PMID 列表
      const searchResult = await this.searchArticles(params);
      
      if (!searchResult.idlist || searchResult.idlist.length === 0) {
        return [];
      }

      // 获取完整的文章信息
      const xmlResponse = await this.getFullArticleInfo(searchResult.idlist);
      
      // 解析 XML 并返回文章信息
      return this.parseArticleXML(xmlResponse);
    } catch (error) {
      console.error('Error in searchAndFetchArticles:', error);
      throw error;
    }
  }
}
