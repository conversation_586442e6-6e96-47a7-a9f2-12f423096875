/**
 * ORCID API 服务
 * 提供 ORCID 作者信息查询和邮箱获取功能
 */

export class ORCIDService {
  private static readonly BASE_URL = 'https://pub.orcid.org/v3.0';
  private static readonly SEARCH_URL = `${ORCIDService.BASE_URL}/search`;

  // 请求队列管理
  private static requestQueue: Array<() => Promise<any>> = [];
  private static isProcessingQueue = false;
  private static readonly REQUEST_DELAY = 1000; // 1秒延迟

  /**
   * 处理请求队列
   */
  private static async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        try {
          await request();
        } catch (error) {
          console.error('Queue request failed:', error);
        }

        // 添加延迟
        if (this.requestQueue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, this.REQUEST_DELAY));
        }
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * 添加请求到队列
   */
  private static addToQueue<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  /**
   * 根据作者姓名搜索 ORCID
   */
  static async searchORCID(firstName: string, lastName: string): Promise<ORCIDSearchResult | null> {
    const query = `given-names:${firstName} AND family-name:${lastName}`;

    return this.addToQueue(async () => {
      try {
        const response = await fetch(`${this.SEARCH_URL}?q=${encodeURIComponent(query)}`, {
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
          }
        });

        if (!response.ok) {
          console.warn(`ORCID search failed: ${response.statusText}`);
          return null;
        }

        const data = await response.json();
        return data;
      } catch (error) {
        console.error('ORCID search error:', error);
        return null;
      }
    });
  }

  /**
   * 获取 ORCID 个人资料
   */
  static async getORCIDProfile(orcidId: string): Promise<ORCIDProfile | null> {
    return this.addToQueue(async () => {
      try {
        const response = await fetch(`${this.BASE_URL}/${orcidId}/person`, {
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
          }
        });

        if (!response.ok) {
          console.warn(`ORCID profile fetch failed: ${response.statusText}`);
          return null;
        }

        const data = await response.json();
        return data;
      } catch (error) {
        console.error('ORCID profile fetch error:', error);
        return null;
      }
    });
  }

  /**
   * 从 ORCID 获取作者邮箱
   */
  static async getAuthorEmail(orcidId: string): Promise<string | null> {
    try {
      const profile = await this.getORCIDProfile(orcidId);
      
      if (!profile?.person?.emails?.email) {
        return null;
      }

      // 查找公开的邮箱
      const publicEmails = profile.person.emails.email.filter(
        email => email.visibility === 'public'
      );

      if (publicEmails.length > 0) {
        return publicEmails[0].email;
      }

      return null;
    } catch (error) {
      console.error('Error getting author email:', error);
      return null;
    }
  }

  /**
   * 增强作者信息（添加 ORCID 和邮箱）
   */
  static async enhanceAuthorInfo(author: PubMedAuthor): Promise<PubMedAuthor> {
    const enhancedAuthor = { ...author };

    try {
      // 如果已经有 ORCID，直接获取邮箱
      if (author.orcid) {
        const email = await this.getAuthorEmail(author.orcid);
        if (email) {
          enhancedAuthor.email = email;
        }
        return enhancedAuthor;
      }

      // 如果没有 ORCID，尝试搜索
      if (author.forename && author.lastname) {
        const searchResult = await this.searchORCID(author.forename, author.lastname);
        
        if (searchResult && searchResult.result && searchResult.result.length > 0) {
          // 取第一个匹配结果
          const firstResult = searchResult.result[0];
          const orcidId = firstResult['orcid-identifier'].path;
          
          enhancedAuthor.orcid = orcidId;
          
          // 尝试获取邮箱
          const email = await this.getAuthorEmail(orcidId);
          if (email) {
            enhancedAuthor.email = email;
          }
        }
      }
    } catch (error) {
      console.error('Error enhancing author info:', error);
    }

    return enhancedAuthor;
  }

  /**
   * 批量增强作者信息（使用队列自动处理延迟）
   */
  static async enhanceAuthorsInfo(authors: PubMedAuthor[]): Promise<PubMedAuthor[]> {
    // 使用 Promise.all 并发处理，队列会自动管理延迟
    return Promise.all(
      authors.map(author => this.enhanceAuthorInfo(author))
    );
  }

  /**
   * 提取作者的国家信息
   */
  static extractCountryFromAffiliation(affiliation: string): string | undefined {
    if (!affiliation) return undefined;

    // 常见国家名称和缩写的正则表达式
    const countryPatterns = [
      // 完整国家名
      /\b(United States|USA|US)\b/i,
      /\b(United Kingdom|UK|Britain)\b/i,
      /\b(China|People's Republic of China|PRC)\b/i,
      /\b(Japan|Nippon)\b/i,
      /\b(Germany|Deutschland)\b/i,
      /\b(France|République française)\b/i,
      /\b(Italy|Italia)\b/i,
      /\b(Spain|España)\b/i,
      /\b(Canada)\b/i,
      /\b(Australia)\b/i,
      /\b(Brazil|Brasil)\b/i,
      /\b(India|Bharat)\b/i,
      /\b(South Korea|Korea)\b/i,
      /\b(Netherlands|Holland)\b/i,
      /\b(Switzerland|Schweiz)\b/i,
      /\b(Sweden|Sverige)\b/i,
      /\b(Norway|Norge)\b/i,
      /\b(Denmark|Danmark)\b/i,
      /\b(Finland|Suomi)\b/i,
      /\b(Belgium|Belgique)\b/i,
      /\b(Austria|Österreich)\b/i,
      /\b(Russia|Russian Federation)\b/i,
      /\b(Israel)\b/i,
      /\b(Singapore)\b/i,
      /\b(Taiwan|Republic of China)\b/i,
      /\b(Hong Kong)\b/i,
      /\b(South Africa)\b/i,
      /\b(Mexico|México)\b/i,
      /\b(Argentina)\b/i,
      /\b(Chile)\b/i,
      /\b(Poland|Polska)\b/i,
      /\b(Czech Republic|Czechia)\b/i,
      /\b(Hungary|Magyarország)\b/i,
      /\b(Portugal)\b/i,
      /\b(Greece|Hellas)\b/i,
      /\b(Turkey|Türkiye)\b/i,
      /\b(Iran|Islamic Republic of Iran)\b/i,
      /\b(Egypt)\b/i,
      /\b(Saudi Arabia)\b/i,
      /\b(United Arab Emirates|UAE)\b/i,
      /\b(Thailand)\b/i,
      /\b(Malaysia)\b/i,
      /\b(Indonesia)\b/i,
      /\b(Philippines)\b/i,
      /\b(Vietnam)\b/i,
      /\b(New Zealand)\b/i
    ];

    for (const pattern of countryPatterns) {
      const match = affiliation.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return undefined;
  }

  /**
   * 提取作者的机构信息
   */
  static extractInstitutionFromAffiliation(affiliation: string): string | undefined {
    if (!affiliation) return undefined;

    // 常见机构类型的关键词
    const institutionKeywords = [
      'University', 'College', 'Institute', 'Hospital', 'Medical Center',
      'Research Center', 'Laboratory', 'School', 'Department', 'Faculty',
      'Academy', 'Foundation', 'Corporation', 'Company', 'Ltd', 'Inc'
    ];

    // 尝试提取包含机构关键词的部分
    const sentences = affiliation.split(/[,;.]/);
    
    for (const sentence of sentences) {
      const trimmed = sentence.trim();
      for (const keyword of institutionKeywords) {
        if (trimmed.toLowerCase().includes(keyword.toLowerCase())) {
          return trimmed;
        }
      }
    }

    // 如果没有找到明确的机构，返回第一个句子
    return sentences[0]?.trim();
  }

  /**
   * 生成作者的外部链接
   */
  static generateAuthorLinks(author: PubMedAuthor): { researchgate?: string; linkedin?: string } {
    const links: { researchgate?: string; linkedin?: string } = {};
    
    if (author.forename && author.lastname) {
      const fullName = `${author.forename} ${author.lastname}`;
      
      // ResearchGate 搜索链接
      const rgQuery = encodeURIComponent(fullName);
      links.researchgate = `https://www.researchgate.net/search/researcher?q=${rgQuery}`;
      
      // LinkedIn 搜索链接
      const liQuery = encodeURIComponent(fullName);
      links.linkedin = `https://www.linkedin.com/search/results/people/?keywords=${liQuery}`;
    }
    
    return links;
  }
}
