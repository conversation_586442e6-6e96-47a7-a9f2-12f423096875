import { AuthorDetailFoPubmed } from "./types";

const PUBMED_API_BASE = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils";

export class PubmedService {
  // 通过关键词搜索
  static async searchByKeywords(keywords: string, page: number = 1, retMax: number = 100): Promise<{results: AuthorDetailFoPubmed[], total: number}> {
    const retStart = (page - 1) * retMax;

    try {
      // 第一步：使用 esearch 获取文章ID列表
      const searchUrl = `${PUBMED_API_BASE}/esearch.fcgi?db=pubmed&term=${encodeURIComponent(keywords)}&retstart=${retStart}&retmax=${retMax}&retmode=json&sort=relevance`;
      const searchResponse = await fetch(searchUrl);
      const searchData = await searchResponse.json();
      
      if (!searchData.esearchresult?.idlist?.length) {
        return { results: [], total: 0 };
      }

      // 第二步：使用 esummary 获取文章详细信息
      const results = await this.fetchArticleDetails(searchData.esearchresult.idlist);
      return {
        results,
        total: parseInt(searchData.esearchresult.count) || 0
      };
    } catch (error) {
      console.error('PubMed关键词搜索错误:', error);
      throw new Error('PubMed搜索失败，请稍后重试');
    }
  }

  // 通过PMID搜索
  static async searchByPMID(pmid:string) {
    try {
      // 直接使用 esummary 获取文章详情
      return await this.fetchArticleDetails([pmid]);
    } catch (error) {
      console.error('PubMed PMID搜索错误:', error);
      throw new Error('PubMed搜索失败，请稍后重试');
    }
  }

  // 获取文章详情并提取作者信息
  static async fetchArticleDetails(pmids:string[]) {
    console.log(pmids);
    try {
      const summaryUrl = `${PUBMED_API_BASE}/efetch.fcgi?db=pubmed&id=${pmids.join(',')}&retmode=xml`;
      const response = await fetch(summaryUrl);
      const xmlText = await response.text();
      
      // 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, "text/xml");
      const reviewers = new Map();
      xmlDoc.querySelectorAll("PubmedArticle").forEach(doc=>{

        // 提取作者信息
        const authors = doc.querySelectorAll('Author[ValidYN="Y"]');

        authors.forEach(author => {
            const lastName = author.querySelector('LastName')?.textContent ||'';
            const foreName = author.querySelector('ForeName')?.textContent ||'';
            const affiliation = author.querySelector('AffiliationInfo Affiliation')?.textContent || '';
            
            // 提取邮箱
            const emailMatch = affiliation.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g);
            const email = emailMatch ? emailMatch[0] : '';
            
            // 提取ORCID
            const orcidMatch = affiliation.match(/\d{4}-\d{4}-\d{4}-\d{4}/);
            const orcid = orcidMatch ? `https://orcid.org/${orcidMatch[0]}` : '';

            const authorKey = `${lastName}-${foreName}`;
            
            if (!reviewers.has(authorKey) && lastName && foreName) {
            reviewers.set(authorKey, {
                name: `${lastName} ${foreName}`,
                firstName: foreName,
                lastName: lastName,
                institution: this.formatAffiliation(affiliation),
                email: email,
                orcid: orcid,
                mesh: Array.from(doc.querySelectorAll("MeshHeadingList MeshHeading DescriptorName")).map(item => item.textContent || ''), // 从文章关键词中提取
                expertise: Array.from(doc.querySelectorAll("KeywordList Keyword")).map(item => item.textContent || ''), // 从文章关键词中提取
                publications: [{
                pmid: doc.querySelector('PMID')?.textContent ||'',
                // 其他文章信息将从ArticleTitle等标签中提取
                title: doc.querySelector('ArticleTitle')?.textContent ||'',
                issn: doc.querySelector('Journal ISSN')?.textContent ||'',
                journal: doc.querySelector('Journal Title')?.textContent || '',
                pubDate: doc.querySelector('PubDate Year')?.textContent|| '',
                IFQuartile:""
                }]
            });
            }
        });

        // 提取关键词
        // const keywords = Array.from(doc.querySelectorAll('Keyword')).map(k => k.textContent);
        
        // // 将关键词添加到所有作者的expertise中
        // reviewers.forEach(reviewer => {
        //     reviewer.expertise = keywords;
        // });

        
      })
      return Array.from(reviewers.values());
    } catch (error) {
      console.error('获取文章详情错误:', error);
      throw error;
    }
  }

  // 解析作者姓名
  static parseAuthorName(fullName:string) {
    // 处理常见的姓名格式：姓 名、姓名缩写等
    const parts = fullName.split(' ');
    if (parts.length === 1) {
      return { lastName: parts[0], firstName: '' };
    }
    
    const lastName = parts[0];
    const firstName = parts.slice(1).join(' ');
    
    return { lastName, firstName };
  }

  // 格式化机构信息
  static formatAffiliation(affiliation:string) {
    // 移除多余的空格和换行
    return affiliation
      .replace(/\s+/g, ' ')
      .replace(/\n/g, ', ')
      .trim();
  }
}