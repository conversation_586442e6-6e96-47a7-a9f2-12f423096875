import { AuthorDetail, SearchResult, SearchProgress } from './types';
import { parsePubMedXML } from '../utils/xml-parser';
import { initJCRDatabase, closeJCRDatabase } from './utils';

const PUBMED_API_BASE = 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils';
const PUBMED_API_KEY = '502310bed10d4831c9b4b16a38bf61816508'; // 建议配置到环境变量

interface JournalInfo {
  Journal: string;
  ISSN: string;
  EISSN: string;
  "IF Quartile": string;
  Category: string;
}

let journalDatabase: JournalInfo[] = [];


// 根据期刊名称或 ISSN 获取 JCR 分区
function getJournalQuartile(journalName: string, issn?: string): string {
  const journal = journalDatabase.find(j => 
    j.Journal.toLowerCase() === journalName.toLowerCase() ||
    (issn && (j.ISSN === issn || j.EISSN === issn))
  );
  return journal?.["IF Quartile"] || 'Unknown';
}

// 批量获取文章作者信息
async function getArticlesByPMIDs(pmids: string[]): Promise<any[]> {
  const batchSize = 50;
  const batches = [];
  
  for (let i = 0; i < pmids.length; i += batchSize) {
    batches.push(pmids.slice(i, i + batchSize));
  }

  const results = [];
  for (const batch of batches) {
    const url = `${PUBMED_API_BASE}/efetch.fcgi?db=pubmed&id=${batch.join(',')}&format=json&api_key=${PUBMED_API_KEY}`;
    const response = await fetch(url);
    const data = await response.json();
    results.push(...data.articles);
  }

  return results;
}

// 获取JCR分区信息
async function getJCRQuartile(journalName: string): Promise<string> {
  // 这里需要实现获取期刊JCR分区的逻辑
  // 可以通过维护一个期刊分区数据库或调用相关API
  return 'Q1'; // 示例返回
}

// 从PubMed文章中提取作者信息
function extractAuthorInfo(article: any): AuthorDetail[] {
  const authors: AuthorDetail[] = [];
  if (article.authors) {
    for (const author of article.authors) {
      authors.push({
          first_name: author.firstname || '',
          last_name: author.lastname || '',
          email_addr: author.email || '',
          affiliation: author.affiliation || '',
          expertise: [],
          publications: 0,
          recent_publications: 0,
          name: `${author.firstname || ''} ${author.lastname || ''}`,
          email: author.email || '',
          orcid: author.orcid || '',
          role: author.role || '',
          institution: author.institution || '',
          country: author.country || '',
          city: author.city || '',
          state: author.state || '',
          zip: author.zip || '',
          display_name: '',
          daisng_id: '',
          seq_no: '',
          full_name: '',
          addr_no: '',
          wos_standard: '',
          claim_status: false,
          r_id: ''
      });
    }
  }
  return authors;
}

export async function searchAuthors(
  keyword: string,
  limit: number,
  yearRange: number,
  jcrFilters: string[],
  progressCallback: (progress: SearchProgress) => void
): Promise<SearchResult> {
  const currentYear = new Date().getFullYear();
  const startYear = currentYear - yearRange;
  
  // 初始化JCR数据库
  initJCRDatabase();

  try {
    // 步骤1: 搜索文章
    progressCallback({
      current: 0,
      total: 100,
      stage: '搜索文章',
      subStage: '正在查询PubMed...'
    });

    // 构建PubMed搜索查询
    const searchQuery = `${keyword} AND ("${startYear}"[Date - Publication] : "${currentYear}"[Date - Publication])`;
    const searchUrl = `${PUBMED_API_BASE}/esearch.fcgi?db=pubmed&term=${encodeURIComponent(searchQuery)}&retmax=${limit}&format=json&api_key=${PUBMED_API_KEY}`;
    
    const searchResponse = await fetch(searchUrl);
    const searchData = await searchResponse.json();
    const articleIds = searchData.esearchresult.idlist;

    // 步骤2: 获取文章详情
    progressCallback({
      current: 20,
      total: 100,
      stage: '获取文章详情',
      subStage: `处理中... 0/${articleIds.length}`
    });

    const detailsUrl = `${PUBMED_API_BASE}/efetch.fcgi?db=pubmed&id=${articleIds.join(',')}&format=xml&api_key=${PUBMED_API_KEY}`;
    const detailsResponse = await fetch(detailsUrl);
    // 处理返回的XML
    const articlesData = await detailsResponse.text();
    const data =  parsePubMedXML(articlesData)
    console.log(data)

    // 步骤3: 处理作者信息
    progressCallback({
      current: 40,
      total: 100,
      stage: '处理作者信息',
      subStage: '分析作者发表情况'
    });

    const authorMap = new Map<string, AuthorDetail>();
    
    for (const article of articlesData.articles) {
      const journalName = article.journal?.name;
      const jcrInfo = getJournalQuartile(journalName);
      const jcrQuartile = jcrInfo?.ifQuartile || 'Unknown';
      
      // 只处理符合JCR分区要求的文章
      if (jcrFilters.includes(jcrQuartile)) {
        const authors = extractAuthorInfo(article);
        
        for (const author of authors) {
          const key = `${author.first_name}${author.last_name}`;
          if (!authorMap.has(key)) {
            authorMap.set(key, {
              ...author,
              recent_publications: 1,
              latest_jcr: jcrQuartile
            });
          } else {
            const existing = authorMap.get(key)!;
            existing.recent_publications = (existing.recent_publications || 0) + 1;
            // 更新最新的JCR分区
            if (!existing.latest_jcr || jcrQuartile < existing.latest_jcr) {
              existing.latest_jcr = jcrQuartile;
            }
          }
        }
      }
    }

    progressCallback({
      current: 90,
      total: 100,
      stage: '完成',
      subStage: '整理搜索结果'
    });

    const authors = Array.from(authorMap.values())
      .sort((a, b) => (b.recent_publications || 0) - (a.recent_publications || 0));

    return {
      authors,
      total: authors.length
    };

  } catch (error) {
    console.error('搜索作者失败:', error);
    throw new Error('搜索作者失败');
  } finally {
    // 关闭数据库连接
    closeJCRDatabase();
  }
}

// 新增批量搜索接口
export const searchAuthorsByPMIDs = async (
  pmids: string[],
  jcrFilter: string[],
  onProgress: (progress: SearchProgress) => void
): Promise<SearchResult> => {
  onProgress({
    current: 0,
    total: pmids.length,
    stage: "搜索PubMed文章",
    subStage: "正在获取XML数据"
  });

  const url = `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=${pmids.join(',')}&retmode=xml`;
  
  const response = await fetch(url);
  const xmlText = await response.text();
  const authors = parsePubMedXML(xmlText);

  // 过滤和处理 JCR 信息
  const filteredAuthors = authors.filter(author => {
    const jcrInfo = getJCRInfo(author.latest_jcr);
    if (!jcrInfo) return false;
    return jcrFilter.includes(jcrInfo['IF Quartile']);
  });

  return {
    authors: filteredAuthors,
    total: filteredAuthors.length
  };
};

export async function findEmailByOrcid(orcid: string): Promise<string> {
  try {
    // 先尝试通过ORCID获取邮箱
    const personInfo = await getOrcidPerson(orcid);
    const primaryEmail = personInfo.emails.email.find(e => e.primary)?.email;
    if (primaryEmail) return primaryEmail;

    // 如果没有邮箱，尝试通过作者ID查询
    const authorId = await getAuthorIdByOrcid(orcid);
    if (authorId) {
      const publications = await getAuthorPublications(authorId);
      // 处理publications中的邮箱信息
      // ...根据实际返回数据结构补充处理逻辑
    }

    return personInfo.emails.email[0]?.email || '';
  } catch (error) {
    console.error('Failed to fetch email:', error);
    throw error;
  }
}

export const getOrcidPerson = async (orcid: string): Promise<OrcidPerson> => {
  const response = await fetch(`https://pub.orcid.org/v3.0/${orcid}/person`, {
    headers: {
      'Accept': 'application/json',
    }
  });

  if (!response.ok) {
    throw new Error('Failed to fetch person info from ORCID');
  }

  return await response.json();
};

export const getAuthorPublications = async (authorId: string): Promise<any> => {
    await delay(1000); // 添加1秒延时

    const url = `https://webofscience.clarivate.cn/api/wosnx/core/runQuerySearch`;
  
    const requestBody = {
        product: "WOS",
        searchMode: "author_publications",
        viewType: "search",
        serviceMode: "summary",
        search: {
            mode: "author_publications",
            database: "WOS",
            authorId: {
                type: "rid",
                value: authorId
            },
            searchOptions: {
                collections: ["WOS", "PPRN"],
                publonCollections: [
                    "ARCI", "BIOABS", "BCI", "BIOSIS", "CABI", 
                    "CSCD", "DRCI", "INSPEC", "KJD", "MEDLINE", 
                    "RSCI", "SCIELO", "ZOOREC", "FSTA", "CCC", "PQDT"
                ],
                nonIndexed: false
            }
        },
        retrieve: {
            view: "summary",
            sort: "date-descending",
            jcr: true,
            history: false,
            count: 50,
            first: 1,
            analyzes: [],
            locale: "zh-cn"
        },
        eventMode: null
    };

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody),
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Failed to fetch author publications');
        }

        return await response.json();
    } catch (error) {
        console.error('Failed to get author publications:', error);
        throw error;
    }
};

// 添加辅助函数：通过ORCID获取作者ID
const getAuthorIdByOrcid = async (orcid: string): Promise<string | null> => {
  // TODO: 实现从ORCID到作者ID的映射逻辑
  return null;
};
function delay(arg0: number) {
    throw new Error('Function not implemented.');
}

