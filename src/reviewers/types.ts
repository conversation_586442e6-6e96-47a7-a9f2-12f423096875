export interface Author {
  name: string;
  email?: string;
  orcid?: string;
  affiliation?: string;
  publications?: number;
  wosId?: string;
  details?: any;  // 存储详细信息
}

export interface SearchResult {
  authors: AuthorDetailFoPubmed[];
  total: number;
  wosIds?: string[];  // 存储所有文章的 WOS ID
}


export interface RecordsiOfSearchDetail {
  id: number;
  key: string;
  payload: {
    [key: string]: {
      doctypes: string[];
      keywords:string[];
      names: {
        author: {
          en: AuthorDetail[];
        }
      };
      titles: {
        item: {
          en: [{title: string}];
        };
        source: {
          en: [{title: string}];
        };
      };
      abstract: {
        basic: {
          en: {
            abstract: string;
          }
        }
      };
      publishers: Publisher[];
      identifiers: Identifier[];
      doi: string;
      pub_info: PubInfo;
    }
  }
}

export interface AuthorDetailFoPubmed {
  affiliation: string;
  emails: never[];
  email:string
  mesh:string[]
  expertise:string[]
  firstName:string
  lastName:string
  institution:string
  name:string
  orcid:string
  publications:{
    pmid:string,
    title:string,
    pubDate:string,
    journal:string,
    issn:string,
    IFQuartile:string
  }[]
}

export interface AuthorDetail {
  first_name: string;
  last_name: string;
  email_addr?: string;
  affiliation?: string;
  recent_publications?: number;
  expertise?: string[];
  latest_jcr?: string;
  publications_by_year?: {
    year: number;
    count: number;
  }[];
  name: any;
  email: string;
  publications: number;
  orcid: any;
  role: string;
  reprint?: string;
  display_name: string;
  daisng_id: string;
  seq_no: string;
  full_name: string;
  emails?:string[]
  addr_no: string;
  wos_standard: string;
  claim_status: boolean;
  institution:string;
  country: string
  city: string,
  state:  string,
  zip:  string,
  r_id: string;
  journal_quartile?: string;
}

interface Publisher {
  names: {
    role: string;
    display_name: string;
    seq_no: string;
    full_name: string;
    addr_no: string;
  }[];
  address_spec: {
    city: string;
    country: string | null;
    full_address: string;
  };
}

interface Identifier {
  type: string;
  value: string;
}

interface PubInfo {
  coverdate: string;
  vol: string;
  pubyear: string;
  issue: string;
  sortdate: string;
  pubmonth: string;
  pubtype: string;
  page_count: string;
}

export interface OrcidPerson {
  emails: {
    email: OrcidEmail[];
    path: string;
    "last-modified-date": {
      value: number;
    };
  };
  name: {
    "given-names": { value: string };
    "family-name": { value: string };
    path: string;
  };
  // 其他字段根据需要添加
}

export interface OrcidEmail {
  email: string;
  primary: boolean;
  verified: boolean;
  visibility: string;
  "created-date": {
    value: number;
  };
  "last-modified-date": {
    value: number;
  };
}

export interface AuthorPublicationsRequest {
  type: 'rid';
  value: string;
}

export interface AuthorPublicationsResponse {
  // 可以根据实际返回数据补充具体字段
  records: any[];
  total?: number;
}

export interface SearchInfo {
  id: number;
  key: string;
  payload: SearchInfoPayload;
}

interface SearchInfoPayload {
  QueryID: string;
  RecordsSearched: number;
  RecordsFound: number;
  RecordsAvailable: number;
}

export interface SearchProgress {
  current: number;
  total: number;
  stage: string;
  subStage?: string;
  subProgress?: {
    current: number;
    total: number;
  };
}

export interface JCRInfo {
  issn: string;
  title: string;
  quartile?: string;
  impactFactor?: number;
  category?: string;
}