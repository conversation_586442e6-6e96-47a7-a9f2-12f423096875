// JATS XML校验器类型定义

export interface ValidationError {
  type: 'error' | 'warning' | 'info';
  code: string;
  message: string;
  line?: number;
  column?: number;
  element?: string;
  attribute?: string;
  context?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  info: ValidationError[];
  summary: {
    totalErrors: number;
    totalWarnings: number;
    totalInfo: number;
    processingTime: number;
  };
}

export interface JATSElement {
  name: string;
  allowedChildren?: string[];
  requiredChildren?: string[];
  allowedAttributes?: string[];
  requiredAttributes?: string[];
  allowedParents?: string[];
  description?: string;
}

export interface JATSAttribute {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'enum' | 'id' | 'idref';
  required?: boolean;
  enumValues?: string[];
  pattern?: string;
  description?: string;
}

export interface JATSValidationRules {
  elements: Record<string, JATSElement>;
  attributes: Record<string, JATSAttribute>;
  globalAttributes: string[];
}

export interface ValidatorOptions {
  checkWellFormedness?: boolean;
  checkJATSCompliance?: boolean;
  checkStructure?: boolean;
  checkAttributes?: boolean;
  strictMode?: boolean;
  maxErrors?: number;
}
