// JATS XML自动修复器

import { ValidationError, ValidationResult } from './types';
import { JATSValidator } from './core';

export interface FixResult {
  fixed: boolean;
  originalXML: string;
  fixedXML: string;
  appliedFixes: string[];
  remainingErrors: ValidationError[];
}

export class JATSAutoFixer {
  private validator: JATSValidator;

  constructor() {
    this.validator = new JATSValidator();
  }

  /**
   * 自动修复JATS XML中的常见问题
   */
  public autoFix(xmlContent: string): FixResult {
    const originalXML = xmlContent;
    let fixedXML = xmlContent;
    const appliedFixes: string[] = [];

    try {
      // 1. 基础格式修复
      fixedXML = this.fixBasicFormatting(fixedXML, appliedFixes);

      // 2. 修复常见的XML问题
      fixedXML = this.fixCommonXMLIssues(fixedXML, appliedFixes);

      // 3. 修复JATS特定问题
      fixedXML = this.fixJATSSpecificIssues(fixedXML, appliedFixes);

      // 4. 修复属性问题
      fixedXML = this.fixAttributeIssues(fixedXML, appliedFixes);

      // 5. 最终格式化
      fixedXML = this.validator.formatXML(fixedXML);
      if (fixedXML !== originalXML) {
        appliedFixes.push('应用XML格式化');
      }

      // 验证修复结果
      const validationResult = this.validator.validate(fixedXML);

      return {
        fixed: appliedFixes.length > 0,
        originalXML,
        fixedXML,
        appliedFixes,
        remainingErrors: validationResult.errors
      };

    } catch (error) {
      return {
        fixed: false,
        originalXML,
        fixedXML: originalXML,
        appliedFixes: [],
        remainingErrors: [{
          type: 'error',
          code: 'AUTO_FIX_ERROR',
          message: `自动修复失败: ${error instanceof Error ? error.message : String(error)}`
        }]
      };
    }
  }

  /**
   * 修复基础格式问题
   */
  private fixBasicFormatting(xml: string, appliedFixes: string[]): string {
    let fixed = xml;

    // 修复XML声明
    if (!fixed.trim().startsWith('<?xml')) {
      fixed = '<?xml version="1.0" encoding="UTF-8"?>\n' + fixed;
      appliedFixes.push('添加XML声明');
    }

    // 修复编码声明
    if (fixed.includes('encoding="utf-8"')) {
      fixed = fixed.replace(/encoding="utf-8"/g, 'encoding="UTF-8"');
      appliedFixes.push('修复编码声明为UTF-8');
    }

    // 移除多余的空白行
    const originalLineCount = fixed.split('\n').length;
    fixed = fixed.replace(/\n\s*\n\s*\n/g, '\n\n');
    if (fixed.split('\n').length !== originalLineCount) {
      appliedFixes.push('移除多余的空白行');
    }

    return fixed;
  }

  /**
   * 修复常见的XML问题
   */
  private fixCommonXMLIssues(xml: string, appliedFixes: string[]): string {
    let fixed = xml;

    // 修复未转义的&字符
    const ampersandMatches = fixed.match(/&(?!(amp|lt|gt|quot|apos|#\d+|#x[0-9a-fA-F]+);)/g);
    if (ampersandMatches) {
      fixed = fixed.replace(/&(?!(amp|lt|gt|quot|apos|#\d+|#x[0-9a-fA-F]+);)/g, '&amp;');
      appliedFixes.push(`修复${ampersandMatches.length}个未转义的&字符`);
    }

    // 修复未转义的<字符（在文本内容中）
    const lessThanMatches = fixed.match(/>([^<]*)<(?![\/\w])/g);
    if (lessThanMatches) {
      fixed = fixed.replace(/>([^<]*)<(?![\/\w])/g, (match, content) => {
        return '>' + content.replace(/</g, '&lt;');
      });
      appliedFixes.push('修复未转义的<字符');
    }

    // 修复自闭合标签格式
    const selfClosingRegex = /<([a-zA-Z][a-zA-Z0-9-]*)[^>]*[^\/]\s*>/g;
    const selfClosingTags = ['br', 'hr', 'img', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'];
    
    fixed = fixed.replace(selfClosingRegex, (match, tagName) => {
      if (selfClosingTags.includes(tagName.toLowerCase())) {
        if (!match.endsWith('/>')) {
          appliedFixes.push(`修复自闭合标签${tagName}的格式`);
          return match.slice(0, -1) + '/>';
        }
      }
      return match;
    });

    return fixed;
  }

  /**
   * 修复JATS特定问题
   */
  private fixJATSSpecificIssues(xml: string, appliedFixes: string[]): string {
    let fixed = xml;

    // 确保有正确的DTD声明
    if (!fixed.includes('<!DOCTYPE article')) {
      const xmlDeclarationEnd = fixed.indexOf('?>');
      if (xmlDeclarationEnd !== -1) {
        const dtd = '\n<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd">';
        fixed = fixed.slice(0, xmlDeclarationEnd + 2) + dtd + fixed.slice(xmlDeclarationEnd + 2);
        appliedFixes.push('添加JATS DTD声明');
      }
    }

    // 确保根元素是article
    if (!fixed.includes('<article')) {
      // 如果没有article标签，尝试包装内容
      const bodyMatch = fixed.match(/<body[^>]*>[\s\S]*<\/body>/);
      if (bodyMatch) {
        const articleStart = '<article article-type="research-article" dtd-version="1.2" xml:lang="en">';
        const articleEnd = '</article>';
        fixed = fixed.replace(bodyMatch[0], articleStart + '\n<front><article-meta><title-group><article-title>Untitled</article-title></title-group></article-meta></front>\n' + bodyMatch[0] + '\n' + articleEnd);
        appliedFixes.push('添加article根元素和基本结构');
      }
    }

    // 确保article元素有必需的属性
    const articleMatch = fixed.match(/<article([^>]*)>/);
    if (articleMatch) {
      let attributes = articleMatch[1];
      let needsUpdate = false;

      if (!attributes.includes('article-type=')) {
        attributes += ' article-type="research-article"';
        needsUpdate = true;
      }
      if (!attributes.includes('dtd-version=')) {
        attributes += ' dtd-version="1.2"';
        needsUpdate = true;
      }
      if (!attributes.includes('xml:lang=')) {
        attributes += ' xml:lang="en"';
        needsUpdate = true;
      }

      if (needsUpdate) {
        fixed = fixed.replace(/<article[^>]*>/, `<article${attributes}>`);
        appliedFixes.push('添加article元素必需属性');
      }
    }

    return fixed;
  }

  /**
   * 修复属性问题
   */
  private fixAttributeIssues(xml: string, appliedFixes: string[]): string {
    let fixed = xml;

    // 修复常见的属性值错误
    const attributeFixes = [
      { pattern: /pub-id-type="publisher"/g, replacement: 'pub-id-type="publisher-id"', description: '修复pub-id-type属性值' },
      { pattern: /contrib-type="authors"/g, replacement: 'contrib-type="author"', description: '修复contrib-type属性值' },
      { pattern: /ref-type="bibliography"/g, replacement: 'ref-type="bibr"', description: '修复ref-type属性值' },
      { pattern: /article-type="article"/g, replacement: 'article-type="research-article"', description: '修复article-type属性值' }
    ];

    attributeFixes.forEach(fix => {
      if (fix.pattern.test(fixed)) {
        fixed = fixed.replace(fix.pattern, fix.replacement);
        appliedFixes.push(fix.description);
      }
    });

    // 修复缺少引号的属性值
    const unquotedAttrRegex = /(\w+)=([^"\s>]+)(?=\s|>)/g;
    fixed = fixed.replace(unquotedAttrRegex, (match, attrName, attrValue) => {
      if (!attrValue.startsWith('"') && !attrValue.startsWith("'")) {
        appliedFixes.push(`为属性${attrName}添加引号`);
        return `${attrName}="${attrValue}"`;
      }
      return match;
    });

    return fixed;
  }

  /**
   * 验证修复结果
   */
  public validateFix(fixResult: FixResult): ValidationResult {
    return this.validator.validate(fixResult.fixedXML);
  }
}

// 便捷函数
export function autoFixJATSXML(xmlContent: string): FixResult {
  const fixer = new JATSAutoFixer();
  return fixer.autoFix(xmlContent);
}
