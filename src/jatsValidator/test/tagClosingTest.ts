// 测试标签闭合逻辑的专门测试

import { jatsValidator } from '../index';

/**
 * 测试用例：同一行多个标签的情况
 */
export const sameLineTagsTest = `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <aff id="aff1">
        <institution content-type="dept">Department of Ultrasound, Medical Imaging Center, the Affiliated Hospital of Yangzhou University</institution>, <institution>Yangzhou University</institution>, <addr-line>Yangzhou</addr-line>, <country country="cn">China</country>
      </aff>
    </article-meta>
  </front>
</article>`;

/**
 * 测试用例：正确的嵌套标签
 */
export const correctNestedTagsTest = `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Zhang</surname>
            <given-names><PERSON><PERSON><PERSON></given-names>
          </name>
          <xref ref-type="aff" rid="aff1">
            <sup>1</sup>
          </xref>
        </contrib>
      </contrib-group>
    </article-meta>
  </front>
</article>`;

/**
 * 测试用例：错误的标签闭合
 */
export const incorrectTagClosingTest = `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Zhang</surname>
            <given-names>Miaomiao
          </name>
        </contrib>
      </contrib-group>
    </article-meta>
  </front>
</article>`;

/**
 * 测试用例：标签不匹配
 */
export const mismatchedTagsTest = `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Zhang</surname>
            <given-names>Miaomiao</given-names>
          </surname>
        </contrib>
      </contrib-group>
    </article-meta>
  </front>
</article>`;

/**
 * 测试用例：自闭合标签
 */
export const selfClosingTagsTest = `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <break/>
      <hr />
      <graphic xlink:href="image.jpg" />
    </article-meta>
  </front>
</article>`;

/**
 * 测试用例：复杂的同行标签结构
 */
export const complexSameLineTest = `<?xml version="1.0" encoding="UTF-8"?>
<article article-type="research-article">
  <front>
    <article-meta>
      <author-notes>
        <corresp id="cor1">
          <italic>Correspondence to:</italic> Hongkui Yu, PhD. Email: <email xlink:href="<EMAIL>"><EMAIL></email>.
        </corresp>
      </author-notes>
    </article-meta>
  </front>
</article>`;

/**
 * 运行标签闭合测试
 */
export function runTagClosingTests() {
  console.log('🧪 开始运行标签闭合测试...');
  
  const testCases = [
    {
      name: '同一行多个标签',
      xml: sameLineTagsTest,
      expectedValid: true,
      description: '测试同一行包含多个标签的情况'
    },
    {
      name: '正确的嵌套标签',
      xml: correctNestedTagsTest,
      expectedValid: true,
      description: '测试正确嵌套的标签结构'
    },
    {
      name: '错误的标签闭合',
      xml: incorrectTagClosingTest,
      expectedValid: false,
      description: '测试未闭合的标签检测'
    },
    {
      name: '标签不匹配',
      xml: mismatchedTagsTest,
      expectedValid: false,
      description: '测试标签名称不匹配的检测'
    },
    {
      name: '自闭合标签',
      xml: selfClosingTagsTest,
      expectedValid: true,
      description: '测试自闭合标签的处理'
    },
    {
      name: '复杂的同行标签',
      xml: complexSameLineTest,
      expectedValid: true,
      description: '测试复杂的同行标签结构'
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    
    const startTime = performance.now();
    const result = jatsValidator.validate(testCase.xml);
    const endTime = performance.now();
    
    const testPassed = result.isValid === testCase.expectedValid;
    
    console.log(`📊 结果: ${result.isValid ? '✅ 有效' : '❌ 无效'}`);
    console.log(`🎯 期望: ${testCase.expectedValid ? '有效' : '无效'}`);
    console.log(`✅ 测试: ${testPassed ? '通过' : '失败'}`);
    console.log(`🐛 错误数量: ${result.errors.length}`);
    console.log(`⚠️ 警告数量: ${result.warnings.length}`);
    console.log(`⏱️ 处理时间: ${(endTime - startTime).toFixed(2)}ms`);
    
    if (result.errors.length > 0) {
      console.log('错误详情:');
      result.errors.forEach(error => {
        console.log(`  - [${error.code}] ${error.message}`);
      });
    }
    
    if (result.warnings.length > 0) {
      console.log('警告详情:');
      result.warnings.forEach(warning => {
        console.log(`  - [${warning.code}] ${warning.message}`);
      });
    }
  });
  
  console.log('\n🎉 标签闭合测试完成！');
}

/**
 * 测试特定的XML内容
 */
export function testSpecificXML(xmlContent: string, description: string = '自定义测试') {
  console.log(`\n🔍 ${description}`);
  
  const startTime = performance.now();
  const result = jatsValidator.validate(xmlContent);
  const endTime = performance.now();
  
  console.log(`📊 校验结果:`);
  console.log(`   状态: ${result.isValid ? '✅ 有效' : '❌ 无效'}`);
  console.log(`   错误: ${result.errors.length} 个`);
  console.log(`   警告: ${result.warnings.length} 个`);
  console.log(`   处理时间: ${(endTime - startTime).toFixed(2)}ms`);
  
  if (result.errors.length > 0) {
    console.log('\n❌ 错误详情:');
    result.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. [${error.code}] ${error.message}`);
      if (error.line) console.log(`      位置: 第${error.line}行`);
    });
  }
  
  if (result.warnings.length > 0) {
    console.log('\n⚠️ 警告详情:');
    result.warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. [${warning.code}] ${warning.message}`);
      if (warning.line) console.log(`      位置: 第${warning.line}行`);
    });
  }
  
  return result;
}

// 导出测试函数
export const tagClosingTests = {
  runTagClosingTests,
  testSpecificXML,
  sameLineTagsTest,
  correctNestedTagsTest,
  incorrectTagClosingTest,
  mismatchedTagsTest,
  selfClosingTagsTest,
  complexSameLineTest
};
