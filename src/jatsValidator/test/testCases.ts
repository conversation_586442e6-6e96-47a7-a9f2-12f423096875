// JATS XML校验器测试用例

export const validJATSExample = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article" dtd-version="1.2" xml:lang="en">
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">example</journal-id>
      <journal-title-group>
        <journal-title>Example Journal</journal-title>
      </journal-title-group>
      <issn pub-type="ppub">1234-5678</issn>
      <publisher>
        <publisher-name>Example Publisher</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
      <article-id pub-id-type="publisher-id">example-001</article-id>
      <article-id pub-id-type="doi">10.1234/example.001</article-id>
      <title-group>
        <article-title>Example Article Title</article-title>
      </title-group>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Smith</surname>
            <given-names>John</given-names>
          </name>
        </contrib>
      </contrib-group>
      <abstract>
        <p>This is an example abstract.</p>
      </abstract>
      <kwd-group>
        <kwd>example</kwd>
        <kwd>JATS</kwd>
      </kwd-group>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>This is the introduction section.</p>
    </sec>
  </body>
</article>`;

export const invalidXMLExample = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article" dtd-version="1.2" xml:lang="en">
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">example</journal-id>
      <journal-title-group>
        <journal-title>Example Journal</journal-title>
      </journal-title-group>
      <issn pub-type="ppub">1234-5678</issn>
      <publisher>
        <publisher-name>Example Publisher</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
      <article-id pub-id-type="publisher-id">example-001</article-id>
      <article-id pub-id-type="doi">10.1234/example.001</article-id>
      <title-group>
        <article-title>Example Article Title
      </title-group>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Smith</surname>
            <given-names>John</given-names>
          </name>
        </contrib>
      </contrib-group>
      <abstract>
        <p>This is an example abstract with unescaped & character.</p>
      </abstract>
      <kwd-group>
        <kwd>example</kwd>
        <kwd>JATS</kwd>
      </kwd-group>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>This is the introduction section.</p>
    </sec>
  </body>
</article>`;

export const missingRequiredElementsExample = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article" dtd-version="1.2" xml:lang="en">
  <front>
    <article-meta>
      <article-id pub-id-type="publisher-id">example-001</article-id>
      <article-id pub-id-type="doi">10.1234/example.001</article-id>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Smith</surname>
            <given-names>John</given-names>
          </name>
        </contrib>
      </contrib-group>
      <abstract>
        <p>This is an example abstract.</p>
      </abstract>
      <kwd-group>
        <kwd>example</kwd>
        <kwd>JATS</kwd>
      </kwd-group>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>This is the introduction section.</p>
    </sec>
  </body>
</article>`;

export const invalidAttributesExample = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="invalid-type" dtd-version="1.2" xml:lang="en">
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">example</journal-id>
      <journal-title-group>
        <journal-title>Example Journal</journal-title>
      </journal-title-group>
      <issn pub-type="ppub">1234-5678</issn>
      <publisher>
        <publisher-name>Example Publisher</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
      <article-id pub-id-type="invalid-id-type">example-001</article-id>
      <article-id pub-id-type="doi">10.1234/example.001</article-id>
      <title-group>
        <article-title>Example Article Title</article-title>
      </title-group>
      <contrib-group>
        <contrib contrib-type="invalid-contrib-type">
          <name>
            <surname>Smith</surname>
            <given-names>John</given-names>
          </name>
        </contrib>
      </contrib-group>
      <abstract>
        <p>This is an example abstract.</p>
      </abstract>
      <kwd-group>
        <kwd>example</kwd>
        <kwd>JATS</kwd>
      </kwd-group>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>This is the introduction section.</p>
    </sec>
  </body>
</article>`;

// 基于demo.xml的真实JATS示例
export const realJATSExample = `<?xml version="1.0" encoding="UTF-8" standalone="no"?><!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd">
<article article-type="research-article" dtd-version="1.2" xml:lang="en" xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
<front>
<journal-meta>
<journal-id journal-id-type="publisher-id">QIMS</journal-id>
<journal-id journal-id-type="nlm-ta">Quant Imaging Med Surg</journal-id>
<journal-title-group>
<journal-title>Quantitative Imaging in Medicine and Surgery</journal-title>
<abbrev-journal-title abbrev-type="pubmed">Quant. Imaging Med. Surg.</abbrev-journal-title>
</journal-title-group>
<issn pub-type="ppub">2223-4292</issn>
<issn pub-type="epub">2223-4306</issn>
<publisher><publisher-name>AME Publishing Company</publisher-name></publisher>
</journal-meta>
<article-meta>
<article-id pub-id-type="publisher-id">qims-24-2439</article-id>
<article-id pub-id-type="doi">10.21037/qims-24-2439</article-id>
<article-categories><subj-group subj-group-type="heading"><subject>Original Article</subject></subj-group>
</article-categories>
<title-group>
<article-title>Cervical sliding sign and cervical funneling in the third trimester as predictors of spontaneous preterm birth in singleton pregnancy</article-title>
</title-group>
<contrib-group>
<contrib contrib-type="author"><name><surname>Zhang</surname><given-names>Miaomiao</given-names></name><xref ref-type="aff" rid="aff1"><sup>1</sup></xref></contrib>
</contrib-group>
<aff id="aff1"><label>1</label><institution content-type="dept">Department of Ultrasonography</institution>, <institution>Shenzhen Baoan Women's and Children's Hospital</institution>, <addr-line>Shenzhen</addr-line>, <country country="cn">China</country></aff>
<author-notes>
<fn id="afn1"><p><italic>Contributions:</italic> (I) Conception and design: H Yu, Y Liu, M Zhang</p></fn>
<corresp id="cor1"><italic>Correspondence to:</italic> Hongkui Yu, PhD. Email: <email xlink:href="<EMAIL>"><EMAIL></email>.</corresp>
</author-notes>
<pub-date pub-type="epub"><day>30</day><month>06</month><year>2025</year></pub-date>
<volume>15</volume>
<issue>7</issue>
<fpage>6005</fpage>
<lpage>6015</lpage>
<history>
<date date-type="received"><day>04</day><month>11</month><year>2024</year></date>
</history>
<permissions>
<copyright-statement>Copyright © 2025 AME Publishing Company. All rights reserved.</copyright-statement>
<copyright-year>2025</copyright-year>
<copyright-holder>AME Publishing Company.</copyright-holder>
<license xlink:href="http://creativecommons.org/licenses/by-nc-nd/4.0/"><license-p><italic>Open Access Statement:</italic> This is an Open Access article. See: <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by-nc-nd/4.0/">https://creativecommons.org/licenses/by-nc-nd/4.0</ext-link>.</license-p></license>
</permissions>
<abstract>
<sec><title>Background</title><p>Spontaneous preterm birth (sPTB) is a major cause of neonatal morbidity and mortality.</p></sec>
</abstract>
<kwd-group kwd-group-type="author"><title>Keywords: </title><kwd>Cervical sliding sign (CSS)</kwd><kwd>funneling</kwd></kwd-group>
<custom-meta-group><custom-meta><meta-name>OPEN-ACCESS</meta-name><meta-value>TRUE</meta-value></custom-meta></custom-meta-group>
</article-meta>
</front>
<body>
<sec sec-type="intro">
<title>Introduction</title>
<p>Spontaneous preterm birth (sPTB) is defined as an unplanned birth before 37 complete weeks of gestation.</p>
</sec>
</body>
</article>`;

export const testCases = [
  {
    name: '有效的JATS XML',
    xml: validJATSExample,
    expectedValid: true,
    description: '这是一个完全符合JATS标准的XML文档'
  },
  {
    name: '无效的XML格式',
    xml: invalidXMLExample,
    expectedValid: false,
    description: '包含未闭合标签和未转义字符的XML文档'
  },
  {
    name: '缺少必需元素',
    xml: missingRequiredElementsExample,
    expectedValid: false,
    description: '缺少journal-meta和title-group等必需元素'
  },
  {
    name: '无效的属性值',
    xml: invalidAttributesExample,
    expectedValid: false,
    description: '包含无效的枚举属性值'
  },
  {
    name: '真实的JATS XML示例',
    xml: realJATSExample,
    expectedValid: true,
    description: '基于demo.xml的真实JATS文档，包含更多元素和属性'
  }
];

// 测试格式化功能的示例
export const unformattedXMLExample = `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd"><article article-type="research-article" dtd-version="1.2" xml:lang="en"><front><journal-meta><journal-id journal-id-type="publisher-id">TEST</journal-id><journal-title-group><journal-title>Test Journal</journal-title></journal-title-group></journal-meta><article-meta><article-id pub-id-type="publisher-id">test-001</article-id><title-group><article-title>Test Article</article-title></title-group></article-meta></front><body><sec><title>Introduction</title><p>This is a test paragraph.</p></sec></body></article>`;

// 运行测试的辅助函数
export function runTests() {
  console.log('开始运行JATS XML校验器测试...');

  testCases.forEach((testCase, index) => {
    console.log(`\n测试 ${index + 1}: ${testCase.name}`);
    console.log(`描述: ${testCase.description}`);

    try {
      // 这里需要导入校验器
      // const result = jatsValidator.validate(testCase.xml);
      // console.log(`结果: ${result.isValid ? '通过' : '失败'}`);
      // console.log(`错误数量: ${result.errors.length}`);
      // console.log(`警告数量: ${result.warnings.length}`);

      console.log('测试准备就绪，请在UI中手动测试');
    } catch (error) {
      console.error(`测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  });
}

// 测试格式化功能
export function testFormatting() {
  console.log('测试XML格式化功能...');
  console.log('原始XML (压缩格式):');
  console.log(unformattedXMLExample);

  // 这里需要导入校验器来测试格式化
  // const formatted = jatsValidator.formatXML(unformattedXMLExample);
  // console.log('\n格式化后的XML:');
  // console.log(formatted);

  console.log('\n请在UI中测试格式化功能');
}
