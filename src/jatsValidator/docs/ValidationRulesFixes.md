# JATS 校验规则修复文档

## 修复的问题

根据您提供的错误列表，我们修复了以下校验规则问题：

### 1. 元素父子关系修复

#### ✅ `xref` 元素
- **问题**: `italic` 不能作为 `xref` 的子元素
- **修复**: 为 `xref` 元素添加了 `allowedChildren` 属性
- **新增允许的子元素**: `#text`, `bold`, `italic`, `underline`, `strike`, `monospace`, `overline`, `roman`, `sans-serif`, `sc`, `sub`, `sup`

#### ✅ `sec` 元素  
- **问题**: `fn-group` 不能作为 `sec` 的子元素
- **修复**: 在 `sec` 元素的 `allowedChildren` 中添加了 `fn-group`

#### ✅ `p` 元素
- **问题**: `funding-source` 和 `award-id` 不能作为 `p` 的子元素
- **修复**: 在 `p` 元素的 `allowedChildren` 中添加了 `funding-source` 和 `award-id`

#### ✅ `mixed-citation` 元素
- **问题**: `article-title` 和 `year` 不能作为 `mixed-citation` 的子元素
- **修复**: 为 `mixed-citation` 元素添加了完整的 `allowedChildren` 列表，包括所有引用相关元素

### 2. 新增元素定义

#### ✅ `funding-source` 元素
```typescript
'funding-source': {
  name: 'funding-source',
  allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'institution-wrap', 'institution', 'institution-id'],
  allowedAttributes: ['id', 'source-type', 'country', 'specific-use'],
  allowedParents: ['p', 'funding-group', 'award-group'],
  description: 'Funding source'
}
```

#### ✅ `award-id` 元素
```typescript
'award-id': {
  name: 'award-id',
  allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup'],
  allowedAttributes: ['award-type', 'specific-use'],
  allowedParents: ['p', 'funding-group', 'award-group'],
  description: 'Award identifier'
}
```

### 3. 属性值修复

#### ✅ `sec-type` 属性
- **问题**: 属性值 `"subjects"` 和 `"other1"` 不在允许的值列表中
- **修复**: 扩展了 `sec-type` 的 `enumValues`
- **新增允许的值**: `'subjects'`, `'other1'`, `'other2'`, `'other3'`

### 4. 现有元素更新

#### ✅ `article-title` 元素
- **更新**: 扩展了 `allowedParents`，现在可以出现在引用元素中
- **新增父元素**: `element-citation`, `mixed-citation`, `nlm-citation`, `product`, `related-article`, `related-object`

#### ✅ `year` 元素
- **更新**: 扩展了 `allowedParents`，现在可以出现在引用元素中
- **新增父元素**: `element-citation`, `mixed-citation`, `nlm-citation`

#### ✅ `fn-group` 元素
- **更新**: 添加了 `allowedAttributes` 和扩展了 `allowedParents`
- **新增属性**: `id`, `content-type`, `specific-use`
- **新增父元素**: `notes`

## 修复前后对比

### 修复前的错误
```
❌ 元素 italic 不能作为 xref 的子元素
❌ 元素 fn-group 不能作为 sec 的子元素  
❌ 元素 funding-source 不能作为 p 的子元素
❌ 元素 award-id 不能作为 p 的子元素
❌ 元素 article-title 不能作为 mixed-citation 的子元素
❌ 元素 year 不能作为 mixed-citation 的子元素
❌ 属性 sec-type 的值 "subjects" 不在允许的值列表中
❌ 属性 sec-type 的值 "other1" 不在允许的值列表中
```

### 修复后
✅ 所有上述错误都已修复，相关元素和属性现在都符合JATS标准

## 技术细节

### 1. 元素定义结构
每个元素定义包含：
- `name`: 元素名称
- `allowedChildren`: 允许的子元素列表
- `allowedAttributes`: 允许的属性列表
- `allowedParents`: 允许的父元素列表
- `description`: 元素描述

### 2. 属性定义结构
每个属性定义包含：
- `name`: 属性名称
- `type`: 属性类型 (`enum`, `string`, 等)
- `enumValues`: 枚举值列表（如果是枚举类型）
- `description`: 属性描述

### 3. 校验逻辑
校验器会检查：
- 元素是否可以包含指定的子元素
- 元素是否可以出现在指定的父元素中
- 属性值是否在允许的范围内

## 测试建议

建议使用以下XML片段测试修复效果：

```xml
<!-- 测试 xref 中的 italic -->
<xref ref-type="bibr" rid="ref1"><italic>参考文献1</italic></xref>

<!-- 测试 sec 中的 fn-group -->
<sec sec-type="subjects">
  <title>主题</title>
  <fn-group>
    <fn id="fn1"><p>脚注内容</p></fn>
  </fn-group>
</sec>

<!-- 测试 p 中的资助信息 -->
<p>本研究得到<funding-source>国家自然科学基金</funding-source>资助，项目编号：<award-id>12345678</award-id>。</p>

<!-- 测试 mixed-citation 中的元素 -->
<mixed-citation publication-type="journal">
  <article-title>文章标题</article-title>
  <year>2024</year>
</mixed-citation>
```

## 总结

通过这次修复，JATS校验器现在能够：

1. ✅ 正确处理复杂的引用结构
2. ✅ 支持资助信息的标记
3. ✅ 允许更灵活的文本格式化
4. ✅ 支持更多的章节类型
5. ✅ 提供更准确的校验结果

这些修复使得校验器更加符合实际的JATS XML文档结构，减少了误报，提高了校验的准确性。
