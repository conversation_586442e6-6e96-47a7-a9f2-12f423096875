// JATS XML标准规则配置

import { JATSValidationRules } from './types';

export const JATS_RULES: JATSValidationRules = {
  elements: {
    'article': {
      name: 'article',
      allowedChildren: ['front', 'body', 'back', 'floats-group', 'processing-meta','sec'],
      requiredChildren: ['front'],
      allowedAttributes: ['article-type', 'dtd-version', 'xml:lang', 'xmlns:xlink', 'xmlns:mml', 'xmlns:xsi'],
      requiredAttributes: ['article-type'],
      description: 'Root element of a JATS article'
    },
    'front': {
      name: 'front',
      allowedChildren: ['journal-meta', 'article-meta', 'def-list', 'notes'],
      requiredChildren: ['journal-meta', 'article-meta'],
      allowedParents: ['article'],
      description: 'Front matter of the article'
    },
    'journal-meta': {
      name: 'journal-meta',
      allowedChildren: ['journal-id', 'journal-title-group', 'issn', 'issn-l', 'publisher'],
      allowedParents: ['front'],
      description: 'Journal metadata'
    },
    'article-meta': {
      name: 'article-meta',
      allowedChildren: [
        'article-id', 'article-categories', 'title-group', 'contrib-group',
        'aff', 'author-notes', 'pub-date', 'volume', 'issue', 'fpage', 'lpage',
        'page-range', 'elocation-id', 'history', 'permissions', 'self-uri',
        'related-article', 'abstract', 'trans-abstract', 'kwd-group', 'funding-group',
        'conference', 'counts', 'custom-meta-group'
      ],
      requiredChildren: ['title-group'],
      allowedParents: ['front'],
      description: 'Article metadata'
    },
    'article-id': {
      name: 'article-id',
      allowedAttributes: ['pub-id-type', 'specific-use'],
      requiredAttributes: ['pub-id-type'],
      allowedParents: ['article-meta'],
      description: 'Article identifier'
    },
    'title-group': {
      name: 'title-group',
      allowedChildren: ['article-title', 'subtitle', 'trans-title-group', 'alt-title'],
      requiredChildren: ['article-title'],
      allowedParents: ['article-meta'],
      description: 'Article title group'
    },
    'article-title': {
      name: 'article-title',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'inline-formula', 'tex-math', 'mml:math', 'inline-graphic', 'private-char', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'named-content', 'styled-content'],
      allowedAttributes: ['specific-use'],
      allowedParents: ['title-group', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object'],
      description: 'Article title'
    },
    'contrib-group': {
      name: 'contrib-group',
      allowedChildren: ['contrib', 'aff', 'aff-alternatives', 'bio', 'email', 'ext-link', 'on-behalf-of', 'role', 'xref'],
      allowedParents: ['article-meta'],
      description: 'Contributor group'
    },
    'contrib': {
      name: 'contrib',
      allowedChildren: ['anonymous', 'collab', 'collab-alternatives', 'name', 'name-alternatives', 'string-name', 'degrees', 'address', 'aff', 'aff-alternatives', 'author-comment', 'bio', 'email', 'ext-link', 'on-behalf-of', 'role', 'uri', 'xref'],
      allowedAttributes: ['contrib-type', 'corresp', 'deceased', 'equal-contrib', 'rid', 'specific-use'],
      allowedParents: ['contrib-group'],
      description: 'Individual contributor'
    },
    'name': {
      name: 'name',
      allowedChildren: ['surname', 'given-names', 'prefix', 'suffix'],
      requiredChildren: ['surname'],
      allowedParents: ['contrib', 'person-group', 'principal-award-recipient', 'principal-investigator'],
      description: 'Personal name'
    },
    'surname': {
      name: 'surname',
      allowedParents: ['name', 'string-name'],
      description: 'Surname or family name'
    },
    'given-names': {
      name: 'given-names',
      allowedParents: ['name', 'string-name'],
      description: 'Given names or first names'
    },
    'body': {
      name: 'body',
      allowedChildren: ['sec', 'p', 'def-list', 'list', 'disp-quote', 'speech', 'statement', 'verse-group', 'table-wrap', 'table-wrap-group', 'alternatives', 'array', 'code', 'fig', 'fig-group', 'graphic', 'media', 'preformat', 'supplementary-material', 'disp-formula', 'disp-formula-group'],
      allowedParents: ['article'],
      description: 'Body of the article'
    },
    'sec': {
      name: 'sec',
      allowedChildren: ['label', 'title', 'sec', 'p', 'def-list', 'list', 'disp-quote', 'speech', 'statement', 'verse-group', 'table-wrap', 'table-wrap-group', 'alternatives', 'array', 'code', 'fig', 'fig-group', 'graphic', 'media', 'preformat', 'supplementary-material', 'disp-formula', 'disp-formula-group', 'fn-group'],
      allowedAttributes: ['id', 'sec-type', 'specific-use'],
      allowedParents: ['abstract', 'article', 'body', 'sec', 'back', 'app', 'boxed-text'],
      description: 'Section'
    },
    'title': {
      name: 'title',
      allowedParents: ['kwd-group', 'caption', 'ack', 'abstract', 'sec', 'app', 'boxed-text', 'fig', 'table-wrap', 'ref-list', 'glossary', 'def-list'],
      description: 'Title'
    },
    'p': {
      name: 'p',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'hr', 'preformat', 'disp-quote', 'speech', 'statement', 'verse-group', 'def-list', 'list', 'alternatives', 'array', 'code', 'graphic', 'media', 'tex-math', 'mml:math', 'chem-struct', 'disp-formula', 'inline-formula', 'inline-graphic', 'private-char', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content', 'funding-source', 'award-id'],
      allowedParents: ['body', 'sec', 'abstract', 'trans-abstract', 'ack', 'app', 'author-comment', 'bio', 'boxed-text', 'caption', 'def', 'disp-quote', 'fig', 'fn', 'glossary', 'list-item', 'note', 'notes', 'ref', 'speech', 'statement', 'supplementary-material', 'table-wrap-foot', 'td', 'th', 'verse-group'],
      description: 'Paragraph'
    },
    'abstract': {
      name: 'abstract',
      allowedChildren: ['label', 'title', 'p', 'sec'],
      allowedAttributes: ['abstract-type', 'specific-use'],
      allowedParents: ['article-meta'],
      description: 'Abstract'
    },
    'kwd-group': {
      name: 'kwd-group',
      allowedChildren: ['label', 'title', 'kwd', 'compound-kwd', 'nested-kwd'],
      allowedAttributes: ['kwd-group-type', 'specific-use'],
      allowedParents: ['article-meta'],
      description: 'Keyword group'
    },
    'kwd': {
      name: 'kwd',
      allowedParents: ['kwd-group', 'compound-kwd', 'nested-kwd'],
      description: 'Keyword'
    },
    'ref-list': {
      name: 'ref-list',
      allowedChildren: ['label', 'title', 'ref'],
      allowedParents: ['back', 'sec', 'app'],
      description: 'Reference list'
    },
    'ref': {
      name: 'ref',
      allowedChildren: ['label', 'element-citation', 'mixed-citation', 'nlm-citation', 'note'],
      allowedAttributes: ['id', 'specific-use'],
      allowedParents: ['ref-list'],
      description: 'Reference'
    },
    'element-citation': {
      name: 'element-citation',
      allowedChildren: ['person-group', 'collab', 'aff', 'aff-alternatives', 'anonymous', 'article-title', 'chapter-title', 'collab-alternatives', 'comment', 'conf-date', 'conf-loc', 'conf-name', 'conf-sponsor', 'data-title', 'date', 'date-in-citation', 'day', 'edition', 'email', 'elocation-id', 'etal', 'ext-link', 'fpage', 'gov', 'institution', 'isbn', 'issn', 'issn-l', 'issue', 'issue-id', 'issue-part', 'issue-title', 'lpage', 'month', 'name', 'object-id', 'page-range', 'part-title', 'patent', 'person-group', 'pub-id', 'publisher-loc', 'publisher-name', 'role', 'season', 'series', 'size', 'source', 'std', 'string-date', 'supplement', 'time-stamp', 'trans-source', 'trans-title', 'uri', 'version', 'volume', 'volume-id', 'volume-series', 'year'],
      allowedAttributes: ['publication-type', 'publication-format', 'publisher-type', 'specific-use'],
      requiredAttributes: ['publication-type'],
      allowedParents: ['ref'],
      description: 'Element citation'
    },
    'mixed-citation': {
      name: 'mixed-citation',
      allowedChildren: ['#text', 'person-group', 'collab', 'aff', 'aff-alternatives', 'anonymous', 'article-title', 'chapter-title', 'collab-alternatives', 'comment', 'conf-date', 'conf-loc', 'conf-name', 'conf-sponsor', 'data-title', 'date', 'date-in-citation', 'day', 'edition', 'email', 'elocation-id', 'etal', 'ext-link', 'fpage', 'gov', 'institution', 'isbn', 'issn', 'issn-l', 'issue', 'issue-id', 'issue-part', 'issue-title', 'lpage', 'month', 'name', 'object-id', 'page-range', 'part-title', 'patent', 'pub-id', 'publisher-loc', 'publisher-name', 'role', 'season', 'series', 'size', 'source', 'std', 'string-date', 'supplement', 'time-stamp', 'trans-source', 'trans-title', 'uri', 'version', 'volume', 'volume-id', 'volume-series', 'year', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break'],
      allowedAttributes: ['publication-type', 'publication-format', 'publisher-type', 'specific-use'],
      requiredAttributes: ['publication-type'],
      allowedParents: ['ref'],
      description: 'Mixed citation'
    },
    'back': {
      name: 'back',
      allowedChildren: ['label', 'title', 'sec', 'ack', 'glossary', 'ref-list', 'notes', 'bio', 'fn-group', 'app-group', 'app'],
      allowedParents: ['article'],
      description: 'Back matter of the article'
    },
    'ack': {
      name: 'ack',
      allowedChildren: ['label', 'title', 'p', 'sec'],
      allowedParents: ['back', 'article-meta'],
      description: 'Acknowledgments'
    },
    'app': {
      name: 'app',
      allowedChildren: ['label', 'title', 'sec', 'p', 'def-list', 'list', 'disp-quote', 'speech', 'statement', 'verse-group', 'table-wrap', 'table-wrap-group', 'alternatives', 'array', 'code', 'fig', 'fig-group', 'graphic', 'media', 'preformat', 'supplementary-material', 'disp-formula', 'disp-formula-group'],
      allowedAttributes: ['id', 'app-type', 'specific-use'],
      allowedParents: ['back', 'app-group'],
      description: 'Appendix'
    },
    'fig': {
      name: 'fig',
      allowedChildren: ['object-id', 'label', 'caption', 'abstract', 'kwd-group', 'subj-group', 'alternatives', 'array', 'code', 'graphic', 'media', 'preformat', 'table-wrap', 'disp-formula', 'disp-formula-group', 'def-list', 'list', 'p', 'related-article', 'related-object', 'disp-quote', 'speech', 'statement', 'verse-group', 'attrib', 'permissions'],
      allowedAttributes: ['id', 'fig-type', 'orientation', 'position', 'specific-use'],
      allowedParents: ['body', 'sec', 'app', 'boxed-text', 'disp-quote', 'fig', 'fig-group', 'glossary', 'ref', 'supplementary-material', 'table-wrap'],
      description: 'Figure'
    },
    'table-wrap': {
      name: 'table-wrap',
      allowedChildren: ['object-id', 'label', 'caption', 'abstract', 'kwd-group', 'subj-group', 'alternatives', 'array', 'code', 'graphic', 'media', 'preformat', 'table', 'disp-formula', 'disp-formula-group', 'def-list', 'list', 'p', 'related-article', 'related-object', 'disp-quote', 'speech', 'statement', 'verse-group', 'attrib', 'permissions', 'table-wrap-foot'],
      allowedAttributes: ['id', 'orientation', 'position', 'specific-use'],
      allowedParents: ['body', 'sec', 'app', 'boxed-text', 'disp-quote', 'fig', 'fig-group', 'glossary', 'ref', 'supplementary-material', 'table-wrap'],
      description: 'Table wrapper'
    },
    'table': {
      name: 'table',
      allowedChildren: ['col', 'colgroup', 'thead', 'tfoot', 'tbody', 'tr'],
      allowedAttributes: ['border', 'cellpadding', 'cellspacing', 'content-type', 'frame', 'rules', 'specific-use', 'style', 'summary', 'width'],
      allowedParents: ['table-wrap', 'alternatives'],
      description: 'Table'
    },
    'tr': {
      name: 'tr',
      allowedChildren: ['th', 'td'],
      allowedAttributes: ['align', 'char', 'charoff', 'content-type', 'specific-use', 'style', 'valign'],
      allowedParents: ['thead', 'tbody', 'tfoot'],
      description: 'Table row'
    },
    'td': {
      name: 'td',
      allowedChildren: ['#text', 'p', 'list', 'def-list', 'alternatives', 'array', 'code', 'graphic', 'media', 'preformat', 'disp-formula', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'disp-quote', 'speech', 'statement', 'verse-group', 'fig', 'fig-group', 'table-wrap', 'table-wrap-group', 'supplementary-material', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'hr', 'inline-graphic', 'private-char', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedAttributes: ['abbr', 'align', 'axis', 'char', 'charoff', 'colspan', 'content-type', 'headers', 'rowspan', 'scope', 'specific-use', 'style', 'valign'],
      allowedParents: ['tr'],
      description: 'Table data cell'
    },
    'th': {
      name: 'th',
      allowedChildren: ['#text', 'p', 'list', 'def-list', 'alternatives', 'array', 'code', 'graphic', 'media', 'preformat', 'disp-formula', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'disp-quote', 'speech', 'statement', 'verse-group', 'fig', 'fig-group', 'table-wrap', 'table-wrap-group', 'supplementary-material', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'hr', 'inline-graphic', 'private-char', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedAttributes: ['abbr', 'align', 'axis', 'char', 'charoff', 'colspan', 'content-type', 'headers', 'rowspan', 'scope', 'specific-use', 'style', 'valign'],
      allowedParents: ['tr'],
      description: 'Table header cell'
    },
    'caption': {
      name: 'caption',
      allowedChildren: ['title', 'p'],
      allowedParents: ['fig', 'table-wrap', 'supplementary-material', 'boxed-text', 'disp-formula', 'chem-struct-wrap', 'disp-formula-group'],
      description: 'Caption'
    },
    'label': {
      name: 'label',
      allowedParents: ['aff', 'sec', 'app', 'boxed-text', 'fig', 'table-wrap', 'ref-list', 'ref', 'glossary', 'def-list', 'def', 'list', 'list-item', 'disp-formula', 'chem-struct-wrap', 'disp-formula-group', 'statement', 'fn', 'note', 'ack', 'bio', 'kwd-group'],
      description: 'Label'
    },
    'xref': {
      name: 'xref',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup'],
      allowedAttributes: ['ref-type', 'rid', 'specific-use'],
      requiredAttributes: ['ref-type', 'rid'],
      allowedParents: ['contrib', 'p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material'],
      description: 'Cross reference'
    },
    'bold': {
      name: 'bold',
      allowedChildren: ['#text', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedParents: ['p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'named-content', 'styled-content'],
      description: 'Bold text'
    },
    'italic': {
      name: 'italic',
      allowedChildren: ['#text', 'bold', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedParents: ['corresp', 'license-p', 'xref', 'p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'bold', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'named-content', 'styled-content'],
      description: 'Italic text'
    },
    'journal-id': {
      name: 'journal-id',
      allowedAttributes: ['journal-id-type', 'specific-use'],
      requiredAttributes: ['journal-id-type'],
      allowedParents: ['journal-meta'],
      description: 'Journal identifier'
    },
    'journal-title-group': {
      name: 'journal-title-group',
      allowedChildren: ['journal-title', 'journal-subtitle', 'trans-title-group', 'abbrev-journal-title'],
      requiredChildren: ['journal-title'],
      allowedParents: ['journal-meta'],
      description: 'Journal title group'
    },
    'journal-title': {
      name: 'journal-title',
      allowedParents: ['journal-title-group'],
      description: 'Journal title'
    },
    'abbrev-journal-title': {
      name: 'abbrev-journal-title',
      allowedAttributes: ['abbrev-type', 'specific-use'],
      allowedParents: ['journal-title-group'],
      description: 'Abbreviated journal title'
    },
    'issn': {
      name: 'issn',
      allowedAttributes: ['pub-type', 'specific-use'],
      allowedParents: ['journal-meta', 'element-citation', 'mixed-citation'],
      description: 'ISSN'
    },
    'publisher': {
      name: 'publisher',
      allowedChildren: ['publisher-name', 'publisher-loc'],
      requiredChildren: ['publisher-name'],
      allowedParents: ['journal-meta'],
      description: 'Publisher information'
    },
    'publisher-name': {
      name: 'publisher-name',
      allowedParents: ['publisher', 'element-citation', 'mixed-citation'],
      description: 'Publisher name'
    },
    'article-categories': {
      name: 'article-categories',
      allowedChildren: ['subj-group', 'series-title', 'series-text'],
      allowedParents: ['article-meta'],
      description: 'Article categories'
    },
    'subj-group': {
      name: 'subj-group',
      allowedChildren: ['subject', 'subj-group', 'compound-subject'],
      allowedAttributes: ['subj-group-type', 'specific-use'],
      allowedParents: ['article-categories', 'subj-group'],
      description: 'Subject group'
    },
    'subject': {
      name: 'subject',
      allowedParents: ['subj-group'],
      description: 'Subject'
    },
    'aff': {
      name: 'aff',
      allowedChildren: ['label', 'addr-line', 'city', 'country', 'fax', 'institution', 'phone', 'postal-code', 'state', 'email', 'ext-link', 'uri', 'break', 'bold', 'italic', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'strike', 'underline', 'sup', 'sub'],
      allowedAttributes: ['id', 'specific-use'],
      allowedParents: ['contrib-group', 'contrib', 'aff-alternatives'],
      description: 'Affiliation'
    },
    'institution': {
      name: 'institution',
      allowedAttributes: ['content-type', 'specific-use'],
      allowedParents: ['aff', 'element-citation', 'mixed-citation'],
      description: 'Institution name'
    },
    'addr-line': {
      name: 'addr-line',
      allowedParents: ['aff', 'address'],
      description: 'Address line'
    },
    'country': {
      name: 'country',
      allowedAttributes: ['country', 'specific-use'],
      allowedParents: ['aff', 'address', 'conference'],
      description: 'Country'
    },
    'author-notes': {
      name: 'author-notes',
      allowedChildren: ['label', 'title', 'corresp', 'fn', 'p'],
      allowedParents: ['article-meta'],
      description: 'Author notes'
    },
    'fn': {
      name: 'fn',
      allowedChildren: ['label', 'p'],
      allowedAttributes: ['id', 'fn-type', 'specific-use'],
      allowedParents: ['author-notes', 'table-wrap-foot', 'fn-group'],
      description: 'Footnote'
    },
    'corresp': {
      name: 'corresp',
      allowedChildren: ['label', 'addr-line', 'city', 'country', 'fax', 'institution', 'phone', 'postal-code', 'state', 'email', 'ext-link', 'uri', 'break', 'bold', 'italic', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'strike', 'underline', 'sup', 'sub'],
      allowedAttributes: ['id', 'specific-use'],
      allowedParents: ['author-notes'],
      description: 'Correspondence information'
    },
    'email': {
      name: 'email',
      allowedAttributes: ['xlink:href', 'specific-use'],
      allowedParents: ['corresp', 'aff', 'contrib', 'person-group'],
      description: 'Email address'
    },
    'pub-date': {
      name: 'pub-date',
      allowedChildren: ['day', 'month', 'year', 'season', 'string-date'],
      allowedAttributes: ['pub-type', 'date-type', 'iso-8601-date', 'specific-use'],
      allowedParents: ['article-meta', 'element-citation', 'mixed-citation'],
      description: 'Publication date'
    },
    'day': {
      name: 'day',
      allowedParents: ['pub-date', 'date', 'date-in-citation'],
      description: 'Day'
    },
    'month': {
      name: 'month',
      allowedParents: ['pub-date', 'date', 'date-in-citation'],
      description: 'Month'
    },
    'year': {
      name: 'year',
      allowedChildren: ['#text'],
      allowedParents: ['pub-date', 'date', 'date-in-citation', 'element-citation', 'mixed-citation', 'nlm-citation'],
      description: 'Year'
    },
    'volume': {
      name: 'volume',
      allowedParents: ['article-meta', 'element-citation', 'mixed-citation'],
      description: 'Volume number'
    },
    'issue': {
      name: 'issue',
      allowedParents: ['article-meta', 'element-citation', 'mixed-citation'],
      description: 'Issue number'
    },
    'fpage': {
      name: 'fpage',
      allowedParents: ['article-meta', 'element-citation', 'mixed-citation'],
      description: 'First page'
    },
    'lpage': {
      name: 'lpage',
      allowedParents: ['article-meta', 'element-citation', 'mixed-citation'],
      description: 'Last page'
    },
    'history': {
      name: 'history',
      allowedChildren: ['date'],
      allowedParents: ['article-meta'],
      description: 'Article history'
    },
    'date': {
      name: 'date',
      allowedChildren: ['day', 'month', 'year', 'season', 'string-date'],
      allowedAttributes: ['date-type', 'iso-8601-date', 'specific-use'],
      allowedParents: ['history', 'element-citation', 'mixed-citation'],
      description: 'Date'
    },
    'permissions': {
      name: 'permissions',
      allowedChildren: ['copyright-statement', 'copyright-year', 'copyright-holder', 'license', 'ali:free_to_read', 'ali:license_ref'],
      allowedParents: ['article-meta', 'fig', 'table-wrap', 'supplementary-material'],
      description: 'Permissions'
    },
    'copyright-statement': {
      name: 'copyright-statement',
      allowedParents: ['permissions'],
      description: 'Copyright statement'
    },
    'copyright-year': {
      name: 'copyright-year',
      allowedParents: ['permissions'],
      description: 'Copyright year'
    },
    'copyright-holder': {
      name: 'copyright-holder',
      allowedParents: ['permissions'],
      description: 'Copyright holder'
    },
    'license': {
      name: 'license',
      allowedChildren: ['license-p', 'ali:free_to_read', 'ali:license_ref'],
      allowedAttributes: ['license-type', 'xlink:href', 'specific-use'],
      allowedParents: ['permissions'],
      description: 'License information'
    },
    'license-p': {
      name: 'license-p',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'ext-link', 'uri', 'email'],
      allowedParents: ['license'],
      description: 'License paragraph'
    },
    'ext-link': {
      name: 'ext-link',
      allowedAttributes: ['ext-link-type', 'xlink:href', 'specific-use'],
      requiredAttributes: ['ext-link-type'],
      allowedParents: ['license-p', 'p', 'td', 'th', 'corresp', 'aff','mixed-citation'],
      description: 'External link'
    },
    'custom-meta-group': {
      name: 'custom-meta-group',
      allowedChildren: ['custom-meta'],
      allowedParents: ['article-meta'],
      description: 'Custom metadata group'
    },
    'custom-meta': {
      name: 'custom-meta',
      allowedChildren: ['meta-name', 'meta-value'],
      requiredChildren: ['meta-name', 'meta-value'],
      allowedParents: ['custom-meta-group'],
      description: 'Custom metadata'
    },
    'meta-name': {
      name: 'meta-name',
      allowedParents: ['custom-meta'],
      description: 'Metadata name'
    },
    'meta-value': {
      name: 'meta-value',
      allowedParents: ['custom-meta'],
      description: 'Metadata value'
    },
    'sup': {
      name: 'sup',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'break', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedParents: ['p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'named-content', 'styled-content','xref'],
      description: 'Superscript text'
    },
    'sub': {
      name: 'sub',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sup', 'break', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedParents: ['p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sup', 'named-content', 'styled-content'],
      description: 'Subscript text'
    },
    'graphic': {
      name: 'graphic',
      allowedAttributes: ['id', 'content-type', 'specific-use', 'xlink:href', 'xlink:title', 'xlink:type', 'mimetype', 'mime-subtype', 'orientation', 'position'],
      allowedParents: ['fig', 'table-wrap', 'supplementary-material', 'app', 'boxed-text', 'disp-quote', 'fig-group', 'glossary', 'ref', 'sec'],
      description: 'Graphic'
    },
    'table-wrap-foot': {
      name: 'table-wrap-foot',
      allowedChildren: ['label', 'title', 'p', 'fn-group', 'fn', 'attrib', 'permissions'],
      allowedParents: ['table-wrap'],
      description: 'Table wrapper footer'
    },
    'fn-group': {
      name: 'fn-group',
      allowedChildren: ['label', 'title', 'fn'],
      allowedAttributes: ['id', 'content-type', 'specific-use'],
      allowedParents: ['table-wrap-foot', 'back', 'sec', 'app', 'notes'],
      description: 'Footnote group'
    },
    'attrib': {
      name: 'attrib',
      allowedParents: ['fig', 'table-wrap', 'supplementary-material', 'boxed-text', 'disp-quote', 'verse-group', 'table-wrap-foot'],
      description: 'Attribution'
    },
    'supplementary-material': {
      name: 'supplementary-material',
      allowedChildren: ['object-id', 'label', 'caption', 'abstract', 'kwd-group', 'subj-group', 'alternatives', 'array', 'code', 'graphic', 'media', 'preformat', 'table-wrap', 'disp-formula', 'disp-formula-group', 'def-list', 'list', 'p', 'related-article', 'related-object', 'disp-quote', 'speech', 'statement', 'verse-group', 'attrib', 'permissions'],
      allowedAttributes: ['id', 'content-type', 'specific-use', 'mimetype', 'mime-subtype', 'orientation', 'position', 'xlink:href', 'xlink:title', 'xlink:type'],
      allowedParents: ['body', 'sec', 'app', 'boxed-text', 'disp-quote', 'fig', 'fig-group', 'glossary', 'ref', 'table-wrap'],
      description: 'Supplementary material'
    },
    'media': {
      name: 'media',
      allowedAttributes: ['id', 'content-type', 'specific-use', 'xlink:href', 'xlink:title', 'xlink:type', 'mimetype', 'mime-subtype', 'orientation', 'position'],
      allowedParents: ['fig', 'table-wrap', 'supplementary-material', 'app', 'boxed-text', 'disp-quote', 'fig-group', 'glossary', 'ref', 'sec'],
      description: 'Media object'
    },
    'object-id': {
      name: 'object-id',
      allowedAttributes: ['pub-id-type', 'specific-use'],
      allowedParents: ['fig', 'table-wrap', 'supplementary-material', 'element-citation', 'mixed-citation'],
      description: 'Object identifier'
    },
    'alternatives': {
      name: 'alternatives',
      allowedChildren: ['array', 'code', 'graphic', 'media', 'preformat', 'table', 'table-wrap', 'textual-form', 'chem-struct', 'disp-formula', 'inline-formula', 'inline-graphic', 'inline-supplementary-material', 'tex-math', 'mml:math'],
      allowedParents: ['fig', 'table-wrap', 'supplementary-material', 'app', 'boxed-text', 'disp-quote', 'fig-group', 'glossary', 'ref', 'sec', 'p', 'td', 'th'],
      description: 'Alternatives'
    },
    'array': {
      name: 'array',
      allowedChildren: ['label', 'caption', 'abstract', 'kwd-group', 'subj-group', 'alternatives', 'graphic', 'media', 'preformat', 'table', 'textual-form', 'attrib', 'permissions'],
      allowedAttributes: ['id', 'content-type', 'specific-use', 'orientation', 'position'],
      allowedParents: ['alternatives', 'fig', 'table-wrap', 'supplementary-material', 'app', 'boxed-text', 'disp-quote', 'fig-group', 'glossary', 'ref', 'sec'],
      description: 'Array'
    },
    'code': {
      name: 'code',
      allowedAttributes: ['id', 'content-type', 'specific-use', 'language', 'language-version', 'platforms', 'position', 'orientation'],
      allowedParents: ['alternatives', 'fig', 'table-wrap', 'supplementary-material', 'app', 'boxed-text', 'disp-quote', 'fig-group', 'glossary', 'ref', 'sec', 'p'],
      description: 'Code'
    },
    'preformat': {
      name: 'preformat',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'hr', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedAttributes: ['id', 'content-type', 'specific-use', 'preformat-type', 'position', 'orientation'],
      allowedParents: ['alternatives', 'fig', 'table-wrap', 'supplementary-material', 'app', 'boxed-text', 'disp-quote', 'fig-group', 'glossary', 'ref', 'sec', 'p', 'td', 'th'],
      description: 'Preformatted text'
    },
    'person-group': {
      name: 'person-group',
      allowedChildren: ['name', 'string-name', 'anonymous', 'collab', 'aff', 'aff-alternatives', 'etal', 'role'],
      allowedAttributes: ['person-group-type', 'specific-use'],
      requiredAttributes: ['person-group-type'],
      allowedParents: ['element-citation', 'mixed-citation', 'nlm-citation', 'product'],
      description: 'Person group in citations'
    },
    'source': {
      name: 'source',
      allowedParents: ['element-citation', 'mixed-citation', 'nlm-citation'],
      description: 'Source of citation'
    },
    'string-date': {
      name: 'string-date',
      allowedParents: ['pub-date', 'date', 'date-in-citation'],
      description: 'String date'
    },
    'season': {
      name: 'season',
      allowedParents: ['pub-date', 'date', 'date-in-citation'],
      description: 'Season'
    },
    'publisher-loc': {
      name: 'publisher-loc',
      allowedParents: ['publisher', 'element-citation', 'mixed-citation'],
      description: 'Publisher location'
    },
    'city': {
      name: 'city',
      allowedParents: ['aff', 'address', 'conference'],
      description: 'City'
    },
    'postal-code': {
      name: 'postal-code',
      allowedParents: ['aff', 'address'],
      description: 'Postal code'
    },
    'state': {
      name: 'state',
      allowedParents: ['aff', 'address'],
      description: 'State or province'
    },
    'phone': {
      name: 'phone',
      allowedParents: ['aff', 'address', 'corresp'],
      description: 'Phone number'
    },
    'fax': {
      name: 'fax',
      allowedParents: ['aff', 'address', 'corresp'],
      description: 'Fax number'
    },
    'uri': {
      name: 'uri',
      allowedParents: ['aff', 'address', 'corresp', 'p', 'license-p', 'td', 'th','contrib'],
      description: 'URI'
    },
    'break': {
      name: 'break',
      allowedParents: ['p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'named-content', 'styled-content', 'aff', 'corresp', 'preformat'],
      description: 'Line break'
    },
    'hr': {
      name: 'hr',
      allowedParents: ['p', 'td', 'th', 'preformat'],
      description: 'Horizontal rule'
    },
    'monospace': {
      name: 'monospace',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedParents: ['p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'bold', 'italic', 'underline', 'strike', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'named-content', 'styled-content', 'aff', 'corresp', 'preformat'],
      description: 'Monospace text'
    },
    'underline': {
      name: 'underline',
      allowedChildren: ['#text', 'bold', 'italic', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedParents: ['p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'bold', 'italic', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'named-content', 'styled-content'],
      description: 'Underlined text'
    },
    'strike': {
      name: 'strike',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'inline-graphic', 'private-char', 'inline-formula', 'tex-math', 'mml:math', 'chem-struct', 'inline-supplementary-material', 'related-article', 'related-object', 'email', 'ext-link', 'uri', 'xref', 'fn', 'target', 'abbrev', 'milestone-end', 'milestone-start', 'named-content', 'styled-content'],
      allowedParents: ['p', 'td', 'th', 'title', 'caption', 'abstract', 'trans-abstract', 'kwd', 'subject', 'compound-kwd-part', 'nested-kwd', 'disp-quote', 'speech', 'statement', 'verse-group', 'def', 'list-item', 'note', 'fn', 'bio', 'boxed-text', 'chem-struct', 'disp-formula', 'element-citation', 'mixed-citation', 'nlm-citation', 'product', 'related-article', 'related-object', 'supplementary-material', 'bold', 'italic', 'underline', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'named-content', 'styled-content'],
      description: 'Strikethrough text'
    },
    'funding-source': {
      name: 'funding-source',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup', 'break', 'institution-wrap', 'institution', 'institution-id'],
      allowedAttributes: ['id', 'source-type', 'country', 'specific-use'],
      allowedParents: ['p', 'funding-group', 'award-group'],
      description: 'Funding source'
    },
    'award-id': {
      name: 'award-id',
      allowedChildren: ['#text', 'bold', 'italic', 'underline', 'strike', 'monospace', 'overline', 'roman', 'sans-serif', 'sc', 'sub', 'sup'],
      allowedAttributes: ['award-type', 'specific-use'],
      allowedParents: ['p', 'funding-group', 'award-group'],
      description: 'Award identifier'
    }
  },
  attributes: {
    'article-type': {
      name: 'article-type',
      type: 'enum',
      enumValues: [
        'abstract', 'addendum', 'announcement', 'article-commentary', 'book-review',
        'brief-report', 'calendar', 'case-report', 'collection', 'correction',
        'data-paper', 'discussion', 'dissertation', 'editorial', 'expression-of-concern',
        'in-brief', 'introduction', 'letter', 'meeting-report', 'news', 'obituary',
        'oration', 'other', 'partial-retraction', 'product-review', 'rapid-communication',
        'reply', 'reprint', 'research-article', 'retraction', 'review-article',
        'translation'
      ],
      description: 'Type of article'
    },
    'pub-id-type': {
      name: 'pub-id-type',
      type: 'enum',
      enumValues: [
        'art-access-id', 'arxiv', 'coden', 'doi', 'doaj', 'handle', 'isbn', 'issn',
        'manuscript', 'medline', 'other', 'pii', 'pmcid', 'pmid', 'publisher-id',
        'sici', 'std-designation'
      ],
      description: 'Type of publication identifier'
    },
    'contrib-type': {
      name: 'contrib-type',
      type: 'enum',
      enumValues: [
        'author', 'editor', 'guest-editor', 'reviewer', 'translator', 'other'
      ],
      description: 'Type of contributor'
    },
    'publication-type': {
      name: 'publication-type',
      type: 'enum',
      enumValues: [
        'book', 'chapter', 'confproc', 'data', 'dataset', 'journal', 'patent',
        'report', 'software', 'standard', 'thesis', 'web', 'working-paper', 'other'
      ],
      description: 'Type of publication'
    },
    'ref-type': {
      name: 'ref-type',
      type: 'enum',
      enumValues: [
        'aff', 'app', 'author-notes', 'bibr', 'bio', 'boxed-text', 'chem',
        'contrib', 'corresp', 'disp-formula', 'fig', 'fn', 'kwd', 'list',
        'other', 'plate', 'scheme', 'sec', 'statement', 'supplementary-material',
        'table', 'table-fn'
      ],
      description: 'Type of reference'
    },
    'sec-type': {
      name: 'sec-type',
      type: 'enum',
      enumValues: [
        'intro', 'introduction', 'methods', 'materials', 'results', 'discussion',
        'conclusion', 'conclusions', 'supplementary-material', 'acknowledgments',
        'ack', 'COI-statement', 'conflict', 'ethics', 'funding', 'subjects',
        'other', 'other1', 'other2', 'other3','cases'
      ],
      description: 'Type of section'
    },
    'app-type': {
      name: 'app-type',
      type: 'enum',
      enumValues: [
        'appendix', 'glossary', 'nomenclature', 'other'
      ],
      description: 'Type of appendix'
    },
    'abstract-type': {
      name: 'abstract-type',
      type: 'enum',
      enumValues: [
        'abstract', 'executive-summary', 'graphical', 'key-points', 'objectives',
        'precis', 'summary', 'synopsis', 'teaser', 'web-summary', 'other'
      ],
      description: 'Type of abstract'
    },
    'kwd-group-type': {
      name: 'kwd-group-type',
      type: 'enum',
      enumValues: [
        'author', 'inspec', 'mesh', 'ocis', 'other'
      ],
      description: 'Type of keyword group'
    },
    'dtd-version': {
      name: 'dtd-version',
      type: 'string',
      pattern: '^[0-9]+\\.[0-9]+$',
      description: 'DTD version number'
    },
    'xml:lang': {
      name: 'xml:lang',
      type: 'string',
      pattern: '^[a-z]{2}(-[A-Z]{2})?$',
      description: 'Language code'
    },
    'journal-id-type': {
      name: 'journal-id-type',
      type: 'enum',
      enumValues: [
        'publisher-id', 'nlm-ta', 'nlm-journal-id', 'iso-abbrev', 'hwp', 'pmc', 'other'
      ],
      description: 'Type of journal identifier'
    },
    'abbrev-type': {
      name: 'abbrev-type',
      type: 'enum',
      enumValues: [
        'pubmed', 'publisher', 'nlm-ta', 'iso', 'other'
      ],
      description: 'Type of abbreviation'
    },
    'pub-type': {
      name: 'pub-type',
      type: 'enum',
      enumValues: [
        'ppub', 'epub', 'collection', 'other'
      ],
      description: 'Publication type'
    },
    'subj-group-type': {
      name: 'subj-group-type',
      type: 'enum',
      enumValues: [
        'heading', 'toc-heading', 'display-heading', 'other'
      ],
      description: 'Type of subject group'
    },
    'content-type': {
      name: 'content-type',
      type: 'string',
      description: 'Content type'
    },
    'fn-type': {
      name: 'fn-type',
      type: 'enum',
      enumValues: [
        'abbr', 'author-note', 'con', 'conflict', 'corresp', 'current-aff',
        'deceased', 'edited-by', 'equal', 'financial-disclosure', 'on-leave',
        'participating-researchers', 'present-address', 'previously-at',
        'study-group-members', 'supplementary-material', 'supported-by', 'other'
      ],
      description: 'Type of footnote'
    },
    'date-type': {
      name: 'date-type',
      type: 'enum',
      enumValues: [
        'accepted', 'corrected', 'pub', 'preprint', 'retracted', 'received',
        'rev-recd', 'rev-request', 'other'
      ],
      description: 'Type of date'
    },
    'ext-link-type': {
      name: 'ext-link-type',
      type: 'enum',
      enumValues: [
        'aoi', 'doi', 'ec', 'email', 'ftp', 'gen', 'genpept', 'highwire',
        'patent', 'pgr', 'pir', 'pirdb', 'pmc', 'pmcid', 'pmid', 'protein',
        'pubmed', 'sprot', 'uri', 'other'
      ],
      description: 'Type of external link'
    },
    'license-type': {
      name: 'license-type',
      type: 'enum',
      enumValues: [
        'open-access', 'copyright', 'ccc', 'other'
      ],
      description: 'Type of license'
    },
    'mimetype': {
      name: 'mimetype',
      type: 'string',
      description: 'MIME type'
    },
    'mime-subtype': {
      name: 'mime-subtype',
      type: 'string',
      description: 'MIME subtype'
    },
    'orientation': {
      name: 'orientation',
      type: 'enum',
      enumValues: ['portrait', 'landscape'],
      description: 'Orientation'
    },
    'position': {
      name: 'position',
      type: 'enum',
      enumValues: ['anchor', 'background', 'float', 'margin'],
      description: 'Position'
    },
    'fig-type': {
      name: 'fig-type',
      type: 'enum',
      enumValues: [
        'figure', 'plate', 'scheme', 'chart', 'graph', 'map', 'video', 'other'
      ],
      description: 'Type of figure'
    },
    'person-group-type': {
      name: 'person-group-type',
      type: 'enum',
      enumValues: [
        'author', 'editor', 'guest-editor', 'reviewer', 'translator', 'other'
      ],
      description: 'Type of person group'
    },
    'country': {
      name: 'country',
      type: 'string',
      description: 'Country code or name'
    },
    'corresp': {
      name: 'corresp',
      type: 'enum',
      enumValues: ['yes', 'no'],
      description: 'Corresponding author indicator'
    },
    'standalone': {
      name: 'standalone',
      type: 'enum',
      enumValues: ['yes', 'no'],
      description: 'XML standalone declaration'
    },
    'xmlns:mml': {
      name: 'xmlns:mml',
      type: 'string',
      pattern: '^http://www\\.w3\\.org/1998/Math/MathML$',
      description: 'MathML namespace'
    },
    'xmlns:xlink': {
      name: 'xmlns:xlink',
      type: 'string',
      pattern: '^http://www\\.w3\\.org/1999/xlink$',
      description: 'XLink namespace'
    },
    'xmlns:xsi': {
      name: 'xmlns:xsi',
      type: 'string',
      pattern: '^http://www\\.w3\\.org/2001/XMLSchema-instance$',
      description: 'XML Schema instance namespace'
    },
    'xlink:href': {
      name: 'xlink:href',
      type: 'string',
      description: 'XLink href attribute'
    },
    'xlink:title': {
      name: 'xlink:title',
      type: 'string',
      description: 'XLink title attribute'
    },
    'xlink:type': {
      name: 'xlink:type',
      type: 'enum',
      enumValues: ['simple', 'extended', 'locator', 'arc', 'resource', 'title'],
      description: 'XLink type attribute'
    },
    'rid': {
      name: 'rid',
      type: 'string',
      description: 'Reference identifier'
    },
    'iso-8601-date': {
      name: 'iso-8601-date',
      type: 'string',
      pattern: '^\\d{4}-\\d{2}-\\d{2}$',
      description: 'ISO 8601 date format'
    }
  },
  globalAttributes: [
    'id', 'xml:lang', 'xml:base', 'specific-use', 'content-type', 'xlink:href',
    'xlink:title', 'xlink:type', 'rid', 'corresp'
  ]
};

// 常见的JATS DTD声明
export const JATS_DTD_DECLARATIONS = [
  '<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.2 20190208//EN" "JATS-archivearticle1.dtd">',
  '<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">',
  '<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Article Authoring DTD v1.2 20190208//EN" "JATS-articleauthoring1.dtd">',
  '<!DOCTYPE article PUBLIC "-//NLM//DTD Journal Publishing DTD v3.0 20080202//EN" "journalpublishing3.dtd">',
  '<!DOCTYPE article PUBLIC "-//NLM//DTD Journal Archiving and Interchange DTD v3.0 20080202//EN" "archivearticle3.dtd">'
];
