import { createSignal, createResource, For, Show } from 'solid-js';
import { PubMedService } from '../services/PubMedService';
import { KeywordMatchingService } from '../services/KeywordMatchingService';
import { ORCIDService } from '../services/ORCIDService';
import * as mammoth from 'mammoth';

const PubMedSearch = () => {
  const [keywords, setKeywords] = createSignal('');
  const [maxResults, setMaxResults] = createSignal(50);
  const [sortBy, setSortBy] = createSignal('relevance');
  const [isSearching, setIsSearching] = createSignal(false);
  const [searchResults, setSearchResults] = createSignal([]);
  const [error, setError] = createSignal('');
  const [uploadedFile, setUploadedFile] = createSignal(null);
  const [extractedContent, setExtractedContent] = createSignal(null);
  const [selectedAuthors, setSelectedAuthors] = createSignal(new Set());

  // 处理文件上传
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.endsWith('.docx')) {
      setError('请上传 .docx 格式的文件');
      return;
    }

    setUploadedFile(file);
    setError('');

    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ arrayBuffer });
      
      // 提取标题（假设第一行是标题）
      const lines = result.value.split('\n').filter(line => line.trim());
      const title = lines[0] || '';
      
      // 从文档内容中提取关键词
      const extractedKeywords = KeywordMatchingService.extractKeywordsFromText(
        result.value, 
        10
      );

      setExtractedContent({
        title,
        keywords: extractedKeywords,
        content: result.value
      });

      // 自动填充关键词
      if (extractedKeywords.length > 0) {
        setKeywords(extractedKeywords.join(', '));
      }

    } catch (err) {
      console.error('文件解析错误:', err);
      setError('文件解析失败，请确保文件格式正确');
    }
  };

  // 执行搜索
  const performSearch = async () => {
    const keywordList = keywords().split(',').map(k => k.trim()).filter(k => k);
    if (keywordList.length === 0) {
      setError('请输入至少一个关键词');
      return;
    }

    setIsSearching(true);
    setError('');
    setSearchResults([]);
    setSelectedAuthors(new Set());

    try {
      // 构建 OR 查询，确保获得更多结果
      const orQuery = keywordList.map(keyword => `"${keyword}"`).join(' OR ');

      const searchParams = {
        query: orQuery,
        maxResults: Math.max(maxResults(), 50), // 至少搜索50篇文章
        sort: sortBy()
      };

      // 获取文章列表
      let articles = await PubMedService.searchAndFetchArticles(searchParams);

      // 如果结果少于20篇，尝试多种扩展搜索策略
      if (articles.length < 20) {
        console.log(`初始搜索仅找到 ${articles.length} 篇文章，尝试扩展搜索...`);

        // 策略1: 移除引号，使用更宽松的搜索
        const expandedQuery1 = keywordList.join(' OR ');
        const expandedParams1 = {
          query: expandedQuery1,
          maxResults: 200,
          sort: sortBy()
        };
        let expandedArticles = await PubMedService.searchAndFetchArticles(expandedParams1);

        // 策略2: 如果还是不够，尝试单个关键词搜索并合并结果
        if (expandedArticles.length < 20) {
          console.log(`扩展搜索找到 ${expandedArticles.length} 篇文章，尝试单关键词搜索...`);
          const allArticles = new Map();

          // 添加已有结果
          expandedArticles.forEach(article => allArticles.set(article.pmid, article));

          // 对每个关键词单独搜索
          for (const keyword of keywordList.slice(0, 3)) { // 限制前3个关键词避免过多API调用
            try {
              const singleKeywordParams = {
                query: keyword,
                maxResults: 50,
                sort: sortBy()
              };
              const singleResults = await PubMedService.searchAndFetchArticles(singleKeywordParams);
              singleResults.forEach(article => allArticles.set(article.pmid, article));

              if (allArticles.size >= 20) break;
            } catch (err) {
              console.warn(`单关键词搜索失败: ${keyword}`, err);
            }
          }

          articles = Array.from(allArticles.values());
        } else {
          articles = expandedArticles;
        }
      }

      if (articles.length === 0) {
        setError('未找到相关文章，请尝试其他关键词');
        setIsSearching(false);
        return;
      }

      // 进行相关性排序
      const processedArticles = KeywordMatchingService.rankArticles(articles, keywordList);

      // 增强作者信息（获取 ORCID 和邮箱）
      const enhancedArticles = await Promise.all(
        processedArticles.slice(0, Math.max(20, processedArticles.length)).map(async (article) => {
          const enhancedAuthors = await ORCIDService.enhanceAuthorsInfo(article.authors);

          // 提取国家和机构信息
          const processedAuthors = enhancedAuthors.map(author => ({
            ...author,
            country: author.affiliation ? ORCIDService.extractCountryFromAffiliation(author.affiliation) : undefined,
            institution: author.affiliation ? ORCIDService.extractInstitutionFromAffiliation(author.affiliation) : undefined
          }));

          return {
            ...article,
            authors: processedAuthors
          };
        })
      );

      // 确保至少有20篇文章
      if (enhancedArticles.length < 20) {
        setError(`仅找到 ${enhancedArticles.length} 篇相关文章，建议添加更多关键词或调整搜索条件`);
      }

      setSearchResults(enhancedArticles);
    } catch (err) {
      console.error('搜索错误:', err);
      setError('搜索失败，请稍后重试');
    } finally {
      setIsSearching(false);
    }
  };

  // 生成作者链接
  const getAuthorLinks = (author) => {
    return ORCIDService.generateAuthorLinks(author);
  };

  // 切换作者选择状态
  const toggleAuthorSelection = (authorKey) => {
    const newSelected = new Set(selectedAuthors());
    if (newSelected.has(authorKey)) {
      newSelected.delete(authorKey);
    } else {
      newSelected.add(authorKey);
    }
    setSelectedAuthors(newSelected);
  };

  // 全选/取消全选作者
  const toggleSelectAll = () => {
    const allAuthors = new Set();
    searchResults().forEach((article, articleIndex) => {
      article.authors.forEach((author, authorIndex) => {
        allAuthors.add(`${articleIndex}-${authorIndex}`);
      });
    });

    if (selectedAuthors().size === allAuthors.size) {
      setSelectedAuthors(new Set());
    } else {
      setSelectedAuthors(allAuthors);
    }
  };

  // 导出选中的审稿人信息
  const exportSelectedReviewers = () => {
    const selectedReviewers = [];

    searchResults().forEach((article, articleIndex) => {
      article.authors.forEach((author, authorIndex) => {
        const authorKey = `${articleIndex}-${authorIndex}`;
        if (selectedAuthors().has(authorKey)) {
          selectedReviewers.push({
            姓名: `${author.forename} ${author.lastname}`,
            邮箱: author.email || '',
            ORCID: author.orcid || '',
            机构: author.institution || author.affiliation || '',
            国家: author.country || '',
            文章标题: article.title,
            期刊: article.journal,
            发表年份: article.pubdate.split('-')[0] || '',
            PMID: article.pmid,
            DOI: article.doi || '',
            ResearchGate: getAuthorLinks(author).researchgate || '',
            LinkedIn: getAuthorLinks(author).linkedin || ''
          });
        }
      });
    });

    if (selectedReviewers.length === 0) {
      setError('请先选择要导出的审稿人');
      return;
    }

    // 转换为CSV格式
    const headers = Object.keys(selectedReviewers[0]);
    const csvContent = [
      headers.join(','),
      ...selectedReviewers.map(reviewer =>
        headers.map(header => `"${reviewer[header] || ''}"`).join(',')
      )
    ].join('\n');

    // 下载CSV文件
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `审稿人信息_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div class="container mx-auto p-6">
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title text-2xl mb-6">PubMed 审稿人查找系统</h2>
          
          {/* 文件上传区域 */}
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">上传 DOCX 文件（可选）</span>
            </label>
            <input 
              type="file" 
              accept=".docx"
              class="file-input file-input-bordered w-full"
              onChange={handleFileUpload}
            />
            <Show when={extractedContent()}>
              <div class="alert alert-success mt-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>文件解析成功！已自动提取标题和关键词</span>
              </div>
            </Show>
          </div>

          {/* 搜索表单 */}
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">研究关键词（用逗号分隔，多个关键词为OR关系）</span>
              <span class="label-text-alt">例如：machine learning, artificial intelligence, deep learning</span>
            </label>
            <textarea
              placeholder="输入研究领域的关键词，用逗号分隔。系统将搜索包含任一关键词的文献，以找到该领域的潜在审稿人"
              class="textarea textarea-bordered w-full h-20"
              value={keywords()}
              onInput={(e) => setKeywords(e.target.value)}
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="form-control">
              <label class="label">
                <span class="label-text">搜索文章数量</span>
              </label>
              <select
                class="select select-bordered w-full"
                value={maxResults()}
                onChange={(e) => setMaxResults(parseInt(e.target.value))}
              >
                <option value={50}>50篇（推荐）</option>
                <option value={100}>100篇</option>
                <option value={200}>200篇</option>
                <option value={500}>500篇</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">排序方式</span>
              </label>
              <select
                class="select select-bordered w-full"
                value={sortBy()}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="relevance">相关性优先</option>
                <option value="date">最新发表优先</option>
              </select>
            </div>
          </div>

          {/* 搜索按钮 */}
          <div class="card-actions justify-end">
            <button 
              class="btn btn-primary"
              onClick={performSearch}
              disabled={isSearching()}
            >
              <Show when={isSearching()}>
                <span class="loading loading-spinner loading-sm"></span>
              </Show>
              {isSearching() ? '搜索审稿人中...' : '查找审稿人'}
            </button>
          </div>

          {/* 错误信息 */}
          <Show when={error()}>
            <div class="alert alert-error mt-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error()}</span>
            </div>
          </Show>
        </div>
      </div>

      {/* 搜索结果 */}
      <Show when={searchResults().length > 0}>
        <div class="mt-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold">找到 {searchResults().length} 篇相关文章</h3>
            <div class="flex gap-2">
              <button
                class="btn btn-sm btn-outline"
                onClick={toggleSelectAll}
              >
                {selectedAuthors().size > 0 ? '取消全选' : '全选审稿人'}
              </button>
              <button
                class="btn btn-sm btn-primary"
                onClick={exportSelectedReviewers}
                disabled={selectedAuthors().size === 0}
              >
                导出选中审稿人 ({selectedAuthors().size})
              </button>
            </div>
          </div>
          <For each={searchResults()}>
            {(article, index) => (
              <div class="card bg-base-100 shadow-lg mb-4">
                <div class="card-body">
                  <div class="flex justify-between items-start mb-2">
                    <h4 class="card-title text-lg">{article.title}</h4>
                    <Show when={article.relevanceScore !== undefined}>
                      <div class="badge badge-primary">
                        相关性: {(article.relevanceScore * 100).toFixed(1)}%
                      </div>
                    </Show>
                  </div>
                  
                  <div class="text-sm text-gray-600 mb-2">
                    <strong>期刊:</strong> {article.journal} | 
                    <strong> 发表日期:</strong> {article.pubdate} |
                    <strong> PMID:</strong> {article.pmid}
                    <Show when={article.doi}>
                      | <strong>DOI:</strong> {article.doi}
                    </Show>
                  </div>

                  <Show when={article.abstract}>
                    <div class="mb-4">
                      <strong>摘要:</strong>
                      <p class="text-sm mt-1">{article.abstract}</p>
                    </div>
                  </Show>

                  {/* 潜在审稿人信息 */}
                  <div class="mb-4">
                    <strong>潜在审稿人:</strong>
                    <div class="overflow-x-auto mt-2">
                      <table class="table table-compact w-full">
                        <thead>
                          <tr>
                            <th>选择</th>
                            <th>姓名</th>
                            <th>机构</th>
                            <th>国家</th>
                            <th>ORCID</th>
                            <th>邮箱</th>
                            <th>外部链接</th>
                          </tr>
                        </thead>
                        <tbody>
                          <For each={article.authors}>
                            {(author, authorIndex) => {
                              const links = getAuthorLinks(author);
                              const authorKey = `${index()}-${authorIndex()}`;
                              return (
                                <tr class={selectedAuthors().has(authorKey) ? 'bg-primary/10' : ''}>
                                  <td>
                                    <input
                                      type="checkbox"
                                      class="checkbox checkbox-sm"
                                      checked={selectedAuthors().has(authorKey)}
                                      onChange={() => toggleAuthorSelection(authorKey)}
                                    />
                                  </td>
                                  <td class="font-medium">{author.forename} {author.lastname}</td>
                                  <td class="text-xs max-w-xs truncate" title={author.institution || author.affiliation || ''}>
                                    {author.institution || author.affiliation || '-'}
                                  </td>
                                  <td>{author.country || '-'}</td>
                                  <td>
                                    <Show when={author.orcid} fallback="-">
                                      <a
                                        href={`https://orcid.org/${author.orcid}`}
                                        target="_blank"
                                        class="link link-primary text-xs"
                                      >
                                        {author.orcid}
                                      </a>
                                    </Show>
                                  </td>
                                  <td>
                                    <Show when={author.email} fallback="-">
                                      <a href={`mailto:${author.email}`} class="link link-primary text-xs">
                                        {author.email}
                                      </a>
                                    </Show>
                                  </td>
                                  <td>
                                    <div class="flex gap-1">
                                      <a
                                        href={links.researchgate}
                                        target="_blank"
                                        class="btn btn-xs btn-outline"
                                        title="ResearchGate搜索"
                                      >
                                        RG
                                      </a>
                                      <a
                                        href={links.linkedin}
                                        target="_blank"
                                        class="btn btn-xs btn-outline"
                                        title="LinkedIn搜索"
                                      >
                                        LI
                                      </a>
                                    </div>
                                  </td>
                                </tr>
                              );
                            }}
                          </For>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <Show when={article.keywords && article.keywords.length > 0}>
                    <div class="mb-2">
                      <strong>关键词:</strong>
                      <div class="flex flex-wrap gap-1 mt-1">
                        <For each={article.keywords}>
                          {(keyword) => (
                            <span class="badge badge-outline badge-sm">{keyword}</span>
                          )}
                        </For>
                      </div>
                    </div>
                  </Show>

                  <Show when={article.keywordMatches && article.keywordMatches.length > 0}>
                    <div>
                      <strong>关键词匹配:</strong>
                      <div class="flex flex-wrap gap-1 mt-1">
                        <For each={article.keywordMatches.filter(match => match.score > 0)}>
                          {(match) => (
                            <span class={`badge badge-sm ${
                              match.type === 'exact' ? 'badge-success' : 
                              match.type === 'partial' ? 'badge-warning' : 'badge-info'
                            }`}>
                              {match.keyword} ({(match.score * 100).toFixed(0)}%)
                            </span>
                          )}
                        </For>
                      </div>
                    </div>
                  </Show>
                </div>
              </div>
            )}
          </For>
        </div>
      </Show>
    </div>
  );
};

export default PubMedSearch;
