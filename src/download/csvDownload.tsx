import { createSignal, For, Show, createEffect } from "solid-js";
import { createStore } from "solid-js/store";

interface CSVRow {
  [key: string]: string;
}

interface DownloadLog {
  filename: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
}

interface DownloadPreview {
  originalUrl: string;
  newFilename: string;
}

interface DownloadState {
  csvContent: CSVRow[];
  headers: string[];
  selectedFields: {[key: string]: boolean};
  filenameField: string;
  previewList: DownloadPreview[];
  isDownloading: boolean;
  isPaused: boolean;
  downloadLogs: DownloadLog[];
  progress: number;
  outputDir: string;
  dirHandle: FileSystemDirectoryHandle | null;
  error: string | null;
}

const CsvDownload = () => {
  const [state, setState] = createStore<DownloadState>({
    csvContent: [],
    headers: [],
    selectedFields: {},
    filenameField: '',
    previewList: [],
    isDownloading: false,
    isPaused: false,
    downloadLogs: [],
    progress: 0,
    outputDir: '',
    dirHandle: null,
    error: null
  });

  const [error, setError] = createSignal<string | null>(null);
  const showErrorMessage = (message: string | null) => {
    setState('error', message);
    if (message) {
      setTimeout(() => setState('error', null), 5000);
    }
  };

  // 获取文件名
  const getFilename = (row: CSVRow, index: number):string => {

    const urlField = state.selectedFields[state.filenameField] ? state.filenameField : Object.keys(state.selectedFields).find(field => state.selectedFields[field]);
    if (!urlField) {
      return "";
    }

    const filename = state.filenameField ? row[state.filenameField] : row[urlField].match(/([^/]+)$/)?.[0];

    return filename || "";

  };

  // 选择输出目录
  const selectOutputDir = async () => {
    try {
      const handle = await window.showDirectoryPicker();
      setState({
        dirHandle: handle,
        outputDir: handle.name
      });
    } catch (err) {
      console.error('目录选择失败:', err);
      showErrorMessage('目录选择失败，请重试');
    }
  };

  // 更新预览列表
  const updatePreviewList = () => {
    const urlField = state.selectedFields[state.filenameField] ? state.filenameField : Object.keys(state.selectedFields).find(field => state.selectedFields[field]);
    if (!urlField) {
      return;
    }
    const previews = state.csvContent.map((row, index) => ({
      originalUrl: row[urlField]?.match(/([^/]+)$/)?.[0] || '',
      newFilename: getFilename(row, index)
    }));
    setState('previewList', previews);
  };

  // 当文件名字段或CSV内容改变时更新预览列表
  createEffect(() => {
    if (state.csvContent.length > 0) {
      updatePreviewList();
    }
  });

  // 显示错误信息
  const showError = (message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000); // 5秒后自动清除错误信息
  };

  // 处理CSV文件选择
  const handleFileSelect = async (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      try {
        // 尝试处理CSV内容
        const rows = text.split('\n')
          .filter(row => row.trim())
          .map(row => {
            const values = row.split(',').map(v => v.trim().replace(/^"|"$/g, ''));
            return values;
          });

        if (rows.length === 0) {
          throw new Error('CSV文件不包含任何数据');
        }

        const headerRow = rows[0];
        const initialSelectedFields = headerRow.reduce((acc, header) => {
          acc[header] = false;
          return acc;
        }, {} as {[key: string]: boolean});

        // 将CSV行转换为对象数组
        const content = rows.slice(1).map((row, rowIndex) => {
          const rowData = headerRow.reduce((acc, header, index) => {
            acc[header] = row[index] || '';
            return acc;
          }, {} as CSVRow);
          
          return rowData;
        });
        
        setState({
          csvContent: content,
          headers: headerRow,
          selectedFields: initialSelectedFields,
          filenameField: '',
          downloadLogs: []
        });

        updatePreviewList();
      } catch (error) {
        console.error('CSV文件处理失败:', error);
        showErrorMessage(error instanceof Error ? error.message : '文件处理失败，请检查CSV格式是否正确');
      }
    };

    reader.onerror = () => {
      showErrorMessage('文件读取失败，请重试');
    };

    reader.readAsText(file);
  };

  // 更新下载状态日志
  const updateLogStatus = (filename: string, status: 'success' | 'error', message?: string) => {
    setState('downloadLogs', prevLogs => 
      prevLogs.map(log => 
        log.filename === filename 
          ? { ...log, status, message } 
          : log
      )
    );
  };

  // 保存文件
  const saveFile = async (directoryHandle: FileSystemDirectoryHandle, filename: string, content: string | Blob) => {
    try {
      const fileHandle = await directoryHandle.getFileHandle(filename, { create: true });
      const writable = await fileHandle.createWritable();
      await writable.write(content);
      await writable.close();
      return true;
    } catch (err) {
      console.error(`保存文件失败: ${filename}`, err);
      throw err;
    }
  };

  // 处理下载
  const handleDownload = async () => {
    if (!state.dirHandle) {
      showErrorMessage('请先选择输出目录');
      return;
    }

    const selectedField = state.headers.find(header => state.selectedFields[header]);
    if (!selectedField) {
      showErrorMessage('请选择包含下载地址的字段');
      return;
    }

    setState({
      isDownloading: true,
      progress: 0,
      isPaused: false,
      downloadLogs: state.csvContent.map((_, index) => ({
        filename: getFilename(state.csvContent[index], index),
        status: 'pending'
      }))
    });

    for (let i = 0; i < state.csvContent.length; i++) {
      if (state.isPaused) {
        break;
      }

      const row = state.csvContent[i];
      const filename = getFilename(row, i);
      try {        // 获取下载地址
        const downloadUrl = row[selectedField];
        if (!downloadUrl) {
          throw new Error('下载地址为空');
        }

        // 下载文件
        const response = await fetch(downloadUrl);
        if (!response.ok) {
          throw new Error(`下载失败: ${response.statusText}`);
        }

        const fileData = await response.blob();
        await saveFile(state.dirHandle, filename, fileData);
        updateLogStatus(filename, 'success');
      } catch (error) {
        console.error(`下载失败: ${filename}`, error);
        updateLogStatus(filename, 'error', error instanceof Error ? error.message : String(error));
      }

      setState('progress', ((i + 1) / state.csvContent.length) * 100);
    }

    setState({
      isDownloading: false,
      isPaused: false
    });
  };

  const handlePauseResume = () => {
    setState('isPaused', !state.isPaused);
  };

  const handleStop = () => {
    setState({
      isDownloading: false,
      isPaused: false
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-emerald-600';
      case 'error': return 'text-rose-600';
      default: return 'text-slate-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success': return '下载成功';
      case 'error': return '下载失败';
      default: return '等待下载';
    }
  };

  return (
    <div class="space-y-8">
      <Show when={state.error}>
        <div class="fixed top-4 right-4 bg-red-50 border-l-4 border-red-500 p-4 rounded shadow-lg z-50 animate-fade-in">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-700">{state.error}</p>
            </div>
          </div>
        </div>
      </Show>
      
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <h1 class="text-2xl font-bold text-slate-800 mb-6">CSV字段下载</h1>
        
        <div class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">CSV文件</label>
            <input
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              class="block w-full text-sm text-slate-500 
                file:mr-4 file:py-2 file:px-4 
                file:rounded-full file:border-0 
                file:text-sm file:font-semibold
                file:bg-blue-50 file:text-blue-700 
                hover:file:bg-blue-100
                cursor-pointer disabled:opacity-50"
              disabled={state.isDownloading}
            />
          </div>

          <Show when={state.headers.length > 0}>
            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">选择用于文件名的字段（可选）</label>
              <select
                value={state.filenameField}
                onChange={(e) => setState('filenameField', e.currentTarget.value)}
                class="select select-bordered w-full"
                disabled={state.isDownloading}
              >
                <option value="">使用默认文件名</option>
                <For each={state.headers}>
                  {(header) => (
                    <option value={header}>{header}</option>
                  )}
                </For>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">选择包含下载地址的字段</label>
              <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <For each={state.headers}>
                  {(header) => (
                    <label class="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="downloadUrlField"
                        checked={state.selectedFields[header]}
                        onChange={() => setState('selectedFields', 
                          Object.keys(state.selectedFields).reduce((acc, key) => ({
                            ...acc,
                            [key]: key === header
                          }), {})
                        )}
                        class="rounded-full border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span class="text-sm text-gray-700">{header}</span>
                    </label>
                  )}
                </For>
              </div>
            </div>
          </Show>

          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">输出目录</label>
            <div class="flex gap-3">
              <input
                type="text"
                value={state.outputDir}
                readonly
                class="block w-full rounded-lg border-slate-200 bg-slate-50 px-4 py-2.5 text-sm"
                placeholder="请选择输出目录"
              />
              <button
                onClick={selectOutputDir}
                disabled={state.isDownloading}
                class="w-[8rem] px-4 py-2.5 text-sm font-medium text-slate-700 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 disabled:opacity-50"
              >
                选择目录
              </button>
            </div>
          </div>
        </div>

        <div class="mt-8 space-y-4">
          <div class="relative">
            <button
              onClick={handleDownload}
              disabled={state.isDownloading || state.csvContent.length === 0 || !state.outputDir || !Object.values(state.selectedFields).some(v => v)}
              class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium
                hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed
                transition-colors duration-200 relative overflow-hidden"
            >
              <div 
                class="absolute left-0 top-0 h-full bg-blue-800/30 transition-all duration-300"
                style={{ width: `${state.progress}%` }}
              />
              <div class="relative z-10 flex items-center justify-center">
                {state.isDownloading ? (
                  <>
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    下载中 ({Math.round(state.progress)}%)
                  </>
                ) : "开始下载"}
              </div>
            </button>
          </div>

          <Show when={state.isDownloading}>
            <div class="flex gap-4">
              <button
                onClick={handlePauseResume}
                class="flex-1 px-6 py-2.5 rounded-lg font-medium border
                  transition-colors duration-200 hover:bg-slate-50 
                  flex items-center justify-center gap-2"
              >
                {state.isPaused ? (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    </svg>
                    <span class="text-green-600">继续下载</span>
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m-9-3a9 9 0 1118 0 9 9 0 01-18 0z" />
                    </svg>
                    <span class="text-yellow-600">暂停下载</span>
                  </>
                )}
              </button>
              
              <button
                onClick={handleStop}
                class="flex-1 px-6 py-2.5 rounded-lg font-medium border
                  text-red-600 hover:bg-red-50
                  transition-colors duration-200
                  flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                </svg>
                停止下载
              </button>
            </div>
          </Show>
        </div>
      </div>

      <Show when={state.downloadLogs.length > 0}>
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-slate-200">
            <h2 class="font-medium text-slate-800">下载日志</h2>
          </div>
          <div class="divide-y max-h-[400px] overflow-auto">
            <For each={state.downloadLogs}>
              {(log) => (
                <div class="px-6 py-3 flex justify-between items-center hover:bg-slate-50">
                  <span class="text-sm text-slate-600">{log.filename}</span>
                  <span class={`text-sm font-medium ${getStatusColor(log.status)}`}>
                    {getStatusText(log.status)}
                    {log.message && ` - ${log.message}`}
                  </span>
                </div>
              )}
            </For>
          </div>
        </div>
      </Show>

      <Show when={state.previewList.length > 0}>
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-slate-200">
            <h2 class="font-medium text-slate-800">预览列表</h2>
          </div>
          <div class="divide-y max-h-[400px] overflow-auto">
            <For each={state.previewList}>
              {(preview) => (
                <div class="px-6 py-3 flex justify-between items-center hover:bg-slate-50">
                  <span class="text-sm text-slate-600">{preview.originalUrl}</span>
                  <span class="text-sm text-slate-600">→</span>
                  <span class="text-sm text-slate-600">{preview.newFilename}</span>
                </div>
              )}
            </For>
          </div>
        </div>
      </Show>

      <Show when={state.error}>
        <div class="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-red-100 text-red-800 px-4 py-2 rounded-lg shadow-md z-50">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12.875A9.003 9.003 0 0112.875 21H11.125A9.003 9.003 0 012 12.875V11.125A9.003 9.003 0 0111.125 2H12.875A9.003 9.003 0 0121 11.125z" />
            </svg>
            <span class="text-sm">{state.error}</span>
          </div>
        </div>
      </Show>
    </div>
  );
};

export default CsvDownload;
