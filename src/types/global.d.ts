declare interface OrcidPerson {
  emails: {
    email: Array<{
      email: string;
      primary: boolean;
    }>;
  };
}

declare interface SearchProgress {
  current: number;
  total: number;
  stage: string;
  subStage?: string;
}

declare interface AuthorDetail {
  first_name: string;
  last_name: string;
  email_addr?: string;
  affiliation?: string;
  mesh?:string[];
  expertise?: string[];
  publications?: number;
  recent_publications?: number;
  latest_jcr?: string;
  name?: string;
  email?: string;
  orcid?: string;
  role?: string;
  institution?: string;
  country?: string;
  city?: string;
  state?: string;
  zip?: string;
  display_name?: string;
  daisng_id?: string;
  seq_no?: string;
  full_name?: string;
  addr_no?: string;
  wos_standard?: string;
  claim_status?: boolean;
  r_id?: string;
}

declare interface SearchResult {
  authors: AuthorDetail[];
  total: number;
}

declare module '*.csv' {
  const content: string;
  export default content;
}
