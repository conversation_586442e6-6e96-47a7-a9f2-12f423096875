declare interface OrcidPerson {
  emails: {
    email: Array<{
      email: string;
      primary: boolean;
    }>;
  };
}

declare interface SearchProgress {
  current: number;
  total: number;
  stage: string;
  subStage?: string;
}

declare interface AuthorDetail {
  first_name: string;
  last_name: string;
  email_addr?: string;
  affiliation?: string;
  mesh?:string[];
  expertise?: string[];
  publications?: number;
  recent_publications?: number;
  latest_jcr?: string;
  name?: string;
  email?: string;
  orcid?: string;
  role?: string;
  institution?: string;
  country?: string;
  city?: string;
  state?: string;
  zip?: string;
  display_name?: string;
  daisng_id?: string;
  seq_no?: string;
  full_name?: string;
  addr_no?: string;
  wos_standard?: string;
  claim_status?: boolean;
  r_id?: string;
}

declare interface SearchResult {
  authors: AuthorDetail[];
  total: number;
}

// PubMed API 相关类型
declare interface PubMedSearchResult {
  idlist: string[];
  count: string;
  retmax: string;
  retstart: string;
  querykey?: string;
  webenv?: string;
}

declare interface PubMedArticle {
  pmid: string;
  title: string;
  abstract?: string;
  authors: PubMedAuthor[];
  journal: string;
  pubdate: string;
  doi?: string;
  keywords?: string[];
  affiliations?: string[];
}

declare interface PubMedAuthor {
  lastname: string;
  forename: string;
  initials: string;
  affiliation?: string;
  orcid?: string;
  email?: string;
  country?: string;
  institution?: string;
}

declare interface PubMedDetailResponse {
  result: {
    [pmid: string]: {
      uid: string;
      title: string;
      authors: Array<{
        name: string;
        authtype: string;
        clusterid?: string;
      }>;
      source: string;
      pubdate: string;
      epubdate?: string;
      volume?: string;
      issue?: string;
      pages?: string;
      articleids: Array<{
        idtype: string;
        value: string;
      }>;
      attributes?: string[];
      pmcrefcount?: number;
      fulljournalname: string;
      elocationid?: string;
      doctype: string;
      srccontriblist?: Array<{
        name: string;
        authtype: string;
        affiliation?: string;
      }>;
    };
  };
}

// ORCID API 相关类型
declare interface ORCIDProfile {
  'orcid-identifier': {
    uri: string;
    path: string;
    host: string;
  };
  person: {
    name: {
      'given-names': { value: string } | null;
      'family-name': { value: string } | null;
    };
    emails: {
      email: Array<{
        email: string;
        verified: boolean;
        visibility: string;
      }>;
    } | null;
  };
}

declare interface ORCIDSearchResult {
  'num-found': number;
  result: Array<{
    'orcid-identifier': {
      uri: string;
      path: string;
      host: string;
    };
  }>;
}

// 关键词匹配相关类型
declare interface KeywordMatch {
  keyword: string;
  score: number;
  type: 'exact' | 'partial' | 'semantic';
}

declare interface ScoredArticle extends PubMedArticle {
  relevanceScore: number;
  keywordMatches: KeywordMatch[];
  hitRate?: number;
  hitCount?: number;
}

// 搜索参数类型
declare interface PubMedSearchParams {
  query: string;
  keywords?: string[];
  maxResults?: number;
  sort?: 'relevance' | 'date' | 'author';
}

// 文件上传相关类型
declare interface DocxContent {
  title?: string;
  keywords?: string[];
  content: string;
}

declare module '*.csv' {
  const content: string;
  export default content;
}
