import {Api} from "../restful/api/restApi";
import <PERSON><PERSON><PERSON>temImpl from "./qimsRefItem";
import ParseDocFile from "./ParseDocFile";
import ParseXmlFile from "./ParseXmlFile";

let selectFiles

let mask = document.getElementById("mask")

let state = {
    select: 1,
    wordSelect: 5
}

function changeFileSelector(value) {
    let fileDom = document.querySelectorAll('#view1 .file_segment')
    Array.prototype.forEach.call(fileDom, (item) => {
        item.className = 'file_segment'
    })
    let currentDom = document.getElementById('File_' + value)
    currentDom.className = 'file_segment active'
}


function updateView(files) {
    let mask = document.getElementById("mask")
    let count = document.getElementById("count")
    let current = document.getElementById("current")
    document.getElementById("convertFile_type").innerText = ''
    document.getElementById("convertFile_type").style.display = "none"
    document.getElementById("download_row").style.display = "none"
    if (!files.length) {
        let elem = document.getElementById("view1")
        elem.innerText = ''
        mask.classList.replace("mask_show", "mask_hide")
        return;
    } else {
        let elem = document.getElementById("view1")
        elem.innerText = ''
        mask.classList.replace("mask_hide", "mask_show")
        count.innerHTML = files.length
        current.innerHTML = "1"
    }
}

function ReadLocaleFiles(inputElement) {
    selectFiles = inputElement.files
    let files = inputElement.files || [];
    updateView(files)
    if (!files.length) {
        return
    }
    let selector = document.getElementById('convertFile_type')
    let parseFile = null
    let fileType = 'xml'
    parseFile = new ParseXmlFile(state.select, selectFiles)
    parseFile.readFiles(selector).then(res => {
        handleResult(res, files, parseFile.zip, fileType)
    })
}

function ReadWordFiles(inputElement) {
    selectFiles = inputElement.files
    let files = inputElement.files || [];
    updateView(files)
    if (!files.length) {
        return
    }
    let selector = document.getElementById('convertFile_type')
    let parseFile = null
    let fileType = 'doc'
    parseFile = new ParseDocFile(state.wordSelect, selectFiles, selector, (res, zip) => {
        handleResult(res, files, zip, fileType)
    })
    parseFile.readFiles()
}


function handleResult(res, files, zip, fileType) {
    document.getElementById("download_row").style.display = "flex"
    if (files.length > 1) {
        document.getElementById("convertFile_type").style.display = "inline-block"
    }
    if (!res) {
        mask.classList.replace("mask_show", "mask_hide")
        document.getElementById("download_row").style.display = "none"
        document.getElementById("convertFile_type").style.display = "none"
        let elem = document.getElementById("view1")
        elem.innerHTML = '文件无具体转换内容,请换个文件操作'
    } else {
        mask.classList.replace("mask_show", "mask_hide")
        document.getElementById("download").addEventListener("click", () => Export2Word(zip, fileType))
    }
}

function combineHTMLContent(html) {
    let preHtml = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title></head><body>";
    let postHtml = "</body></html>";
    return preHtml + html + postHtml;
}

function Export2Word(zip, type) {
    if (selectFiles.length > 1) {
        DownLoadZIP(zip)
    } else {
        let html = combineHTMLContent(document.getElementById('File_0').innerHTML);
        let blob = new Blob(['\ufeff', html], {
            type: 'application/msword'
        });
        // Specify link url
        let url = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(html);
        // Specify file name
        let replacePattern = type === 'doc' ? /\.(doc|docx)/i : /\.xml/i
        let filename = selectFiles[0].name.replace(replacePattern, "")
        filename = filename ? filename + '.doc' : 'document.doc';
        // Create download link element
        let downloadLink = document.createElement("a");
        document.body.appendChild(downloadLink);
        if (navigator.msSaveOrOpenBlob) {
            navigator.msSaveOrOpenBlob(blob, filename);
        } else {
            // Create a link to the file
            downloadLink.href = url;
            // Setting the file name
            downloadLink.download = filename;
            //triggering the function
            downloadLink.click();
        }
        document.body.removeChild(downloadLink);
    }
}

function DownLoadZIP(zip) {
    zip.generateAsync({type: "blob"}).then(function (blob) {
        let a = document.createElement('a');
        a.download = `${new Date().toLocaleString()}.zip`;
        a.href = URL.createObjectURL(blob);
        a.dataset.downloadurl = ["text/html", a.download, a.href].join(':');
        a.style.display = "none";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        setTimeout(function () {
            URL.revokeObjectURL(a.href);
        }, 1500);
    });
}

async function searchPmid(pmid) {
    let refIdList = [{rId: "1", pmid: pmid}]
    await getSummary(pmid).then(res => {
        let refListImpl = new QimsItemImpl(null, 0, refIdList, res, pmid)
        refListImpl.render().then(async html => {

        }, rej => {
            document.getElementById('view1').innerText = rej || ''
        })
    }, rej => {
        document.getElementById('view1').innerText = rej || ''
    })
}

function getSummary(ids): Promise<string> {
    return Api.get("/entrez/eutils/esummary.fcgi", {db: "pubmed", id: ids}, {}).then(res => {
        return res
    })
}

document.onreadystatechange = function () {
    if (document.readyState === "complete") {

        document.getElementById("xml_type").addEventListener('change', function (event: any) {
            state.select = parseInt(event.target.value || 0)
        })
        document.getElementById("word_type").addEventListener('change', function (event: any) {
            state.wordSelect = parseInt(event.target.value || 0)
        })
        document.getElementById('convertFile_type').addEventListener('change', function (e: any) {
            //@ts-ignore
            let value = event.target.value
            changeFileSelector(value)
        })

        document.getElementById("download_row").style.display = "none"
        document.getElementById("convertFile_type").style.display = "none"
        document.getElementById("file").addEventListener("change", async function () {
                ReadLocaleFiles(this)
            }
        )
        document.getElementById("wordFile").addEventListener("change", async function () {
                ReadWordFiles(this)
            }
        )

        document.getElementById('pmidSearch').addEventListener('click', function () {
            //@ts-ignore
            let pmid = document.getElementById('pmidInput').value
            if (!pmid) {
                return
            }
            document.getElementById("download_row").style.display = "none"
            let elem = document.getElementById("view1")
            elem.innerText = ''
            searchPmid(pmid)
        })
    }
}

