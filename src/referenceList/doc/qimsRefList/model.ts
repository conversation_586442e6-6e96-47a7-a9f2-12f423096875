export interface parseDoc {

    render(): Promise<string>
}


export interface DocSumItem {
    id: string,
    authors: string,
    title: string,
    source: string,
    pubDate: string,
    volume: string,
    issue: string,
    pages: string,
    doi: string,
    pubStatus: string,
    eLocationId: string,
    pii: string,
}

export interface IContentId {
    content: string,
    id: number
}

export interface RefIdItem {
    rId: string,
    pmid?: string
}

export interface IArticleId {
    idtype: string,
    idtypen: number,
    value : string
}
export interface IAuthor {
    name: string,
    authtype: string,
    clusterid: string
}
export interface IHistory {
    pubstatus: string,
    date: string
}
export interface IReferences {
    refsource: string,
    reftype: string,
    pmid: number | string,
    note: string
}

export interface IIdResponse {
    uid: string,
    pubdate: string,
    epubdate: string,
    source: string,
    authors: IAuthor[],
    lastauthor: string,
    title: string,
    sorttitle: string,
    volume: string,
    issue: string,
    pages: string,
    lang: string[],
    nlumniqueid: string,
    issn: string,
    essn: string,
    pubtype: string[],
    recordstatus: string,
    pubstatus: string,
    articleids: IArticleId[],
    history: IHistory[],
    references: IReferences[],
    attributes: string[],
    pmcrefcount: number,
    fulljournalname: string,
    elocationid: string,
    doctype: string,
    srccontriblist?: object[],
    booktitle?: string,
    medium?: string,
    edition?: string,
    publisherlocation?: string,
    publishername?: string,
    srcdate?: string,
    reportnumber?: string,
    availablefromurl?: string,
    locationlabel?: string,
    doccontriblist?: object[],
    docdate?: string,
    bookname?: string,
    chapter?: string,
    sortpubdate?: string,
    sortfirstauthor?: string,
    vernaculartitle?: string,
    pii?: string
}

export interface IPmidResult {
    uids: string[],
    [propName: string]: IIdResponse | any

}
export interface IPmidResponse {
    result: IPmidResult
}
