import RefListImpl from "./reflist";
import <PERSON><PERSON><PERSON><PERSON>Impl from "./qimsRefList";
import ZhRefImpl from "./zhRefList";
import CJTSRefImpl from './cjtsRefList';
import J<PERSON>Zip from "jszip";
import {RefIdItem} from "./zhRefList/model";
import {Api} from "../restful/api/restApi";

export default class ParseXmlFile {
    public zip = null;
    private selectFiles = null;
    private select = 1

    constructor(select,selectFiles) {
        this.zip = new JSZip()
        this.select = select
        this.selectFiles = selectFiles
    }

    async readFiles(selector) {
        let promises = []
        let fileArr = []
        let refIdList = []
        let current = document.getElementById("current")
        current.innerHTML = "1"
        await this.readFileRefIds(this.selectFiles).then((res: any) => {
            res.forEach(item => {
                fileArr.push(item)
                refIdList = refIdList.concat(item.refIdList)
            })
        })
        let pmids = refIdList.map(item => item.pmid).filter(pid => pid)
        if (!pmids || !pmids.length) {
            return null
        }
        let refListImpl = null
        await this.getSummary(pmids.join(",")).then(res => {
            fileArr.forEach((item, key) => {
                let p = null
                if (this.selectFiles.length > 1) {
                    let opt = document.createElement('option')
                    opt.value = `${key}`
                    opt.innerHTML = item.fileName
                    selector.appendChild(opt)
                }
                let fileName = item.fileName.replace(/\.xml/i, "")
                try {
                    if (this.select === 1) {
                        refListImpl = new RefListImpl(item.xmlDoc, key, item.refIdList, res)
                    } else if (this.select === 2) {
                        refListImpl = new QimsListImpl(item.xmlDoc, key, item.refIdList, res)
                    } else if (this.select === 3) {
                        refListImpl = new ZhRefImpl(item.xmlDoc, key, item.refIdList, res)
                    } else if (this.select === 4) {
                        refListImpl = new CJTSRefImpl(item.xmlDoc, key, item.refIdList, res)
                    }
                    current.innerHTML = `${key + 1}`
                    p = refListImpl.render().then(async html => {
                        if (key === 0) {
                            this.changeFileSelector(key)
                        }
                        html = this.combineHTMLContent(html);
                        let url = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(html);
                        let blob = await fetch(url).then(r => r.blob());
                        this.zip.file(fileName + ".doc", blob)
                        if (this.selectFiles.length === 1) {
                            return Promise.resolve(html)
                        } else {
                            return Promise.resolve(this.zip)
                        }
                    }, rej => {
                        this.zip.file(fileName + ".txt", rej)
                        return Promise.resolve({Error: rej, item})
                    })
                } catch (e) {
                    this.zip.file(fileName + ".txt", e.toString())
                    return Promise.resolve({Error: e, item})
                }
                promises.push(p)
            })
        })
        return Promise.all(promises)
    }

    async readFileRefIds(files) {
        let promises = []
        Array.prototype.forEach.call(files, (file, key) => {
            let p = this.ReadXML(file).then((xml: string) => {
                let name = file.name.replace(/\.xml/i, '')
                try {
                    let xmlDoc = this.parseXML(xml)
                    let refIds = this.getRefList(xmlDoc)
                    return Promise.resolve({fileName: name, xmlDoc: xml, refIdList: refIds})
                } catch (e) {
                    return Promise.resolve({fileName: name, xmlDoc: xml, refIdList: []})
                }
            })
            promises.push(p)
        })
        return Promise.all(promises)
    }


    async ReadXML(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = (event) => {
                resolve(reader.result)
            }
            reader.readAsText(file);
        })
    }


    parseXML(xmlStr: string): Document {
        let parser = new DOMParser()
        let xmlDoc = parser.parseFromString(xmlStr, 'text/xml')
        return xmlDoc
    }

     getRefList(xmlDoc: Document): RefIdItem[] {
        let list = xmlDoc.querySelectorAll('ref-list ref')
        return Array.prototype.map.call(list, (ref, index) => {
            let rId = index + 1
            let pmid = ref.querySelector("mixed-citation[publication-type=\"journal\"] pub-id[pub-id-type=\"pmid\"]")
            if (!pmid) {
                return {rId}
            } else {
                return {rId, pmid: pmid.textContent}
            }
        })
    }

    getSummary(ids): Promise<string> {
        return Api.get("/entrez/eutils/esummary.fcgi", {db: "pubmed", id: ids}, {}).then(res => {
            return res
        })
    }

    combineHTMLContent(html) {
        let preHtml = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title></head><body>";
        let postHtml = "</body></html>";
        return preHtml + html + postHtml;
    }

    changeFileSelector(value) {
        let fileDom = document.querySelectorAll('#view1 .file_segment')
        Array.prototype.forEach.call(fileDom, (item) => {
            item.className = 'file_segment'
        })
        let currentDom = document.getElementById('File_' + value)
        currentDom.className = 'file_segment active'
    }
}
