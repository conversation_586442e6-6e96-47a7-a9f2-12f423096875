<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>CombinationXML</title>
    <style>
        iframe {
            width: 100vw;
            height: 100vh
        }

        .row {
            display: flex;
            margin: 20px 20px;
            align-items: center;
        }
        .search_row{
            justify-content: space-between;
        }

        .half_row {
            display: flex;
            align-items: center;
        }

        .divider {
            width: 100%;
            height: 1px;
            background: #e0e0e0;
        }

        .mask_show {
            display: flex;
        }

        .mask_hide {
            display: none;
        }

        #mask {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            width: 100vw;
            height: 100vh;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 40px;
        }

        select, #pmidInput {
            width: 200px;
            height: 30px;
            box-sizing: border-box;
            margin-right: 5px;
        }

        #download {
            margin-right: 5px;
        }

        #view1 .file_segment {
            display: none;
        }

        #view1 .file_segment.active {
            display: block;
        }

        #view1 #compareLastId_block {
            border: 1px solid #e0e0e0;
            border-top: none;
            padding: 20px 20px 20px 80px;
        }
    </style>
</head>

<body>
<div class="row">
    <div class="half_row">
        <select name="xml" id="xml_type">
            <option value="1">普通杂志-xml</option>
            <option value="2">qims杂志-xml</option>
            <option value="3">中文杂志-xml</option>
            <option value="4">CJTS中文杂志-xml</option>
        </select>
        <input id="file" type="file" name="submit" value="UpLoad docx" class="btn" multiple
               accept="text/xml,applicatin/xml"/>
    </div>
    <div class="half_row">
        <select name="word" id="word_type">
            <option value="5">普通杂志-word</option>
            <option value="6">qims杂志-word</option>
            <option value="7">中文杂志-word</option>
            <option value="8">CJTS杂志-word</option>
            <option value="9">中文书文献-word</option>
        </select>
        <input id="wordFile" type="file" name="submit" value="UpLoad docx" class="btn" multiple
               accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"/>
    </div>
</div>
<!--<div class="row" id="download_row" style="display: none">-->
<!--    <input id="download" type="button" name="submit" value="DownLoad Word" class="btn">-->
<!--    <select name="xml" id="convertFile_type" style="display: none;">-->
<!--    </select>-->
<!--</div>-->
<div class="divider"></div>
<div class="row search_row">
    <div class="half_row">
        <input id="pmidInput" type="number" placeholder="请输入pmid(qims)"/>
        <input id="pmidSearch" type="button" name="submit" value="Search" class="btn">
    </div>
    <div class="half_row" id="download_row" style="display: none;">
        <input id="download" type="button" name="submit" value="DownLoad Word" class="btn">
        <select name="xml" id="convertFile_type" style="display: none;">
        </select>
    </div>
</div>
<div class="divider"></div>
<div id="view1">
</div>
<div id="view2"></div>
<div id="view3"></div>
<div id="mask" class="mask_hide">
    <p>正在处理第 <span id="current"></span>个，共计<span id="count"></span>个</p>
    <p>正在处理文件...</p>
</div>
</body>

</html>
