import {DocSumItem, RefIdItem} from "../qimsRefList/model";
import {Api} from "../../restful/api/restApi";
import QimsListImpl from "../qimsRefList";


export default class QimsItemImpl extends QimsListImpl {

    pmid: string

    constructor(file, fileKey, refIdList, xmlString, pmid) {
        super(file, fileKey, refIdList, xmlString)
        this.pmid = pmid
    }

    parseXML(xmlStr: string): Document {
        let parser = new DOMParser()
        let xmlDoc = parser.parseFromString(xmlStr, 'text/xml')
        return xmlDoc
    }

    getRefList(xmlDoc: Document): RefIdItem[] {
        return [{rId: "1", pmid: this.pmid}]
    }


    getSummary(ids): Promise<string> {
        return Api.get("/entrez/eutils/esummary.fcgi", {db: "pubmed", id: ids}, {}).then(res => {
            return res
        })
    }


    getDocSumList(refIdList: RefIdItem[], xmlDoc: Document): string[] {
        let list = xmlDoc.querySelectorAll('DocSum')
        if(!list?.length){return []}
        return refIdList.map(child => {
            if (!child.pmid) {
                return ""
            } else {
                let item = Array.prototype.find.call(list, item => child.pmid === item.querySelector('Id').textContent)
                let id = item.querySelector('Id')?.textContent
                let source = item?.querySelector('Item[Name=\"Source\"]')?.textContent
                let pubDate = item.querySelector('Item[Name=\"PubDate\"]')?.textContent
                let volume = item.querySelector('Item[Name=\"Volume\"]')?.textContent
                let issue = item.querySelector('Item[Name=\"Issue\"]')?.textContent
                let authorDoc = item.querySelector("Item[Name=\"AuthorList\"]")
                let authors = this.getAuthors(authorDoc)
                let title = item.querySelector('Item[Name=\"Title\"]')?.textContent?.replace(/\[|\]|<(.*?)>|<\/(.*?)>|<(.*?)\/>/g, "")
                let pages = this.dealPages(item)
                let doi = item.querySelector('Item[Name=\"ArticleIds\"] Item[Name=\"doi\"]')?.textContent
                let pubStatus = item.querySelector('Item[Name=\"PubStatus\"]')?.textContent
                let eLocationId = item.querySelector('Item[Name=\"ELocationID\"]')?.textContent
                let sumItem = {
                    id,
                    title,
                    source,
                    pubDate,
                    volume,
                    issue,
                    pages,
                    doi,
                    pubStatus,
                    authors,
                    eLocationId
                }
                return this.convertDocSumToStr(child, sumItem)
            }
        })

    }


    convertDocSumToStr(refIdItem: RefIdItem, docSumItem: DocSumItem): string {
        let result = ""
        result += docSumItem.authors ? docSumItem.authors + ". " : ""
        result += docSumItem.title ? docSumItem.title + " " : ""
        result += docSumItem.source ? docSumItem.source + " " : ""
        let pubDate = docSumItem.pubDate.split(' ')
        if (docSumItem.pubStatus === 'aheadofprint') {
            result += pubDate.length ? pubDate[0] + ". " : ""
            result += '[Epub ahead of print].'
            result += docSumItem.doi ? ' doi: ' + docSumItem.doi + "." : ""
        } else if (docSumItem.pages) {
            result += pubDate.length ? pubDate[0] + ";" : ""
            result += docSumItem.volume ? docSumItem.volume + ":" : ""
            result += docSumItem.issue&&!docSumItem.volume ? "(" + docSumItem.issue + ")" + ":" : ""
            result += docSumItem.pages ? docSumItem.pages + "." : ""
        } else if (docSumItem.pubStatus && docSumItem.eLocationId) {
            let matchReg = /(?<=pii\:).*?(?=\.)/
            let piiArr = docSumItem.eLocationId.match(matchReg)
            if (!piiArr || !piiArr.length) {
                result += pubDate.length ? pubDate[0] + "." : ""
                result += docSumItem.doi ? ' doi: ' + docSumItem.doi + "." : ""
            } else if (docSumItem.pubStatus === 'epublish') {
                let pii = piiArr.length ? piiArr[0].trim() : ""
                pii = 'E' === pii.charAt(0)?.toUpperCase() ? pii.slice(1) : pii
                result += pubDate.length ? pubDate[0] + ";" : ""
                result += docSumItem.volume ? docSumItem.volume + ":" : ""
                result += docSumItem.issue&&!docSumItem.volume ? "(" + docSumItem.issue + ")" + ":" : ""
                result += pii ? pii + "." : ""
            } else if (docSumItem.pubStatus === 'ppublish'
                || docSumItem.pubStatus === 'ppublish+epublish'
                || docSumItem.pubStatus === 'epublish+ppublish') {
                let pii = piiArr.length ? piiArr[0].trim() : ""
                result += pubDate.length ? pubDate[0] + ";" : ""
                result += docSumItem.volume ? docSumItem.volume + ":" : ""
                result += docSumItem.issue&&!docSumItem.volume ? "(" + docSumItem.issue + ")" + ":" : ""
                result += pii ? pii + "." : ""
            }
        } else if (pubDate.length) {
            result += pubDate[0] + ". "
        }
        return result
    }


    convertSummary(refIdList: RefIdItem[], xmlString: string): string[] {
        let xmlDoc = this.parseXML(xmlString)
        let docSumList = this.getDocSumList(refIdList, xmlDoc)
        return docSumList
    }

    getSummaryError(xmlString: string): string {
        let xmlDoc = this.parseXML(xmlString)
        let error = xmlDoc.querySelector('ERROR')?.textContent
        return error || '无具体转换内容,请换个pmid重试'
    }


    appendToView(docStr: string[]): string {
        let elem = document.getElementById('view1')
        let childElem = document.createElement('div')
        childElem.id = `File_${this.fileKey}`
        childElem.className = 'file_segment active'
        let ulElem = document.createElement('ul')
        docStr.forEach(item => {
            let subElem = document.createElement('ol')
            subElem.style.marginBottom = "10px"
            subElem.innerHTML = item
            ulElem.appendChild(subElem)
        })
        childElem.appendChild(ulElem)
        elem.innerText = ''
        elem.appendChild(childElem)
        return childElem.innerHTML
    }

    render(): Promise<string> {
        let result = this.convertSummary(this.refIdList, this.xmlString)
        if (result && result.length) {
            return Promise.resolve(this.appendToView(result))
        } else {
            let error = this.getSummaryError(this.xmlString)
            return Promise.reject(error)
        }
    }

}
