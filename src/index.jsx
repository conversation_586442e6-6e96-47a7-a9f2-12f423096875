import { HashRouter, Route } from '@solidjs/router';
import { render } from 'solid-js/web';
import './index.css'
import ReferenceList from './referenceList';
import PdfDownload from "./download/batchPDF"
import CsvDownload from "./download/csvDownload"
import ReviewerFinder from "./reviewers"
import AppendixGenerator from "./appendix"
import BatchRename from "./batchRename"
import CSVBatchRename from "./csvBatchRename"
import XmlBatchModifier from "./xmlBatchModifier"
import JATSValidatorUI from "./jatsValidator/components/JATSValidatorUI"

const App = (props) => {
  return (
    <div class="flex flex-col h-screen">
      {/* 顶部导航栏 */}
      <div class="navbar bg-base-100 shadow-lg z-0">
        <div class="flex-1">
          <span class="text-xl font-bold">Ame Plus</span>
        </div>
        <div class="flex-none">
          <button class="btn btn-ghost btn-circle">
            <div class="indicator">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              <span class="badge badge-xs badge-primary indicator-item"></span>
            </div>
          </button>
        </div>
      </div>

      {/* 主要内容区域 - 左右布局 */}
      <div class="flex flex-1 overflow-hidden">
        {/* 左侧菜单 */}
        <div class="w-64 bg-base-200 p-4">
          <ul class="menu bg-base-200 rounded-box">
            <li>
              <a href="/" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                首页
              </a>
            </li>
            <li>
              <a href="/pdfDownload" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
                PDF下载
              </a>
            </li>
            <li>
              <a href="/csvDownload" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                CSV下载
              </a>
            </li>
            <li>
              <a href="/reviewers" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                查找审稿人
              </a>
            </li>
            <li>
              <a href="/appendix" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                文章附录生成
              </a>
            </li>
            <li>
              <a href="/batchRename" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                批量重命名
              </a>
            </li>
            <li>
              <a href="/csvBatchRename" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                CSV批量重命名
              </a>
            </li>
            <li>
              <a href="/xmlBatchModifier" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
                XML批量修改(威科)
              </a>
            </li>
            <li>
              <a href="/jatsValidator" class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                JATS XML校验器
              </a>
            </li>
          </ul>
        </div>

        {/* 右侧内容区域 */}
        <div class="flex-1 p-4 overflow-auto bg-base-100">
          {props.children}
        </div>
      </div>
    </div>
  );
};

render(() => (
  <HashRouter root={App}>
    <Route path="/" component={ReferenceList} />
    <Route path="/pdfDownload" component={PdfDownload} />
    <Route path="/csvDownload" component={CsvDownload} />
    <Route path="/reviewers" component={ReviewerFinder} />
    <Route path="/appendix" component={AppendixGenerator} />
    <Route path="/batchRename" component={BatchRename} />
    <Route path="/csvBatchRename" component={CSVBatchRename} />
    <Route path="/xmlBatchModifier" component={XmlBatchModifier} />
    <Route path="/jatsValidator" component={JATSValidatorUI} />
    <Route path="/*" component={() => <h1>404 - 页面未找到</h1>} />
  </HashRouter>
), document.getElementById('app'));