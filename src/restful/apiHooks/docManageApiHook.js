// import {useEffect, useState} from "preact";
import {ossHttp} from "../api/aliApi";
import {executeRequest} from "../api/http_client"

//
// export function useGetOSSToken() {
//     const [token, setToken] = useState({});
//     useEffect(() => {
//         ossHttp.get("/getOssToken").then(res => {
//             console.log(res)
//             // setToken(JSON.parse(res.token))
//         })
//     });
//
//     return [token];
//
// }

export function getOSSToken(params) {
    return ossHttp.get("/getOssToken", params).then(res => {
        return res
    })
}

export function uploadFile(options, getProgress) {
    const {startsWith, filename} = options;
    let formData = new FormData();
    formData.append('name', `${startsWith}/${filename}`);
    formData.append('key', `${startsWith}/${filename}`);
    formData.append('policy', options.policy);
    formData.append('OSSAccessKeyId', options.OSSAccessKeyId);
    formData.append('success_action_status', 200);
    formData.append('signature', options.signature);
    formData.append('file', options.file);
    formData.append('callback', options.callback);
    let option = {url: options.host, method: 'POST', headers: {}, data: formData};
    let url = `ame-tests/static/public`;
    return executeRequest({...option, getProgress}).then(() => url)
}

export const GetObjectKey = (params) => {
    console.log(params)
    return ossHttp.get("/getObjectMeta", params,)
};

export const GetSymlink = (params) => {
    return ossHttp.get("/getSymlink", params,)
};

export const PutSymlink = (data, metaType) => {
    return ossHttp.put("/putSymlink", data, {
        "content-type": "application/json",
        "x-oss-meta-content-type": metaType || "application/octet-stream",
        "x-oss-symlink-target": data.targetObjectName
    })
};

export const CreateUploadVideo = (params) => {
    return vodHttp.get("/vodServer/createUploadVideo", params)
};

export const GenerateDataKey = (params) => {
    return vodHttp.get("/vodServer/generateDataKey", params)
};

export const SubmitTranscodeJobs = (params) => {
    return vodHttp.get("/vodServer/SubmitTranscodeJobs", params)
};

export const GetPlayInfo = (params) => {
    return vodHttp.get("/vodServer/getPlayInfo", params)
};

export const GetVideoInfo = (params) => {
    return vodHttp.get("/vodServer/getVideoInfo", params)
};

export const SearchMedia = (params) => {
    return vodHttp.get("/vodServer/searchMedia", params)
};

export const GetMtsHlsUriToken = (params) => {
    return vodHttp.get("/vodServer/getMtsHlsUriToken", params)
};

export const GetCategories = (params) => {
    return vodHttp.get("/vodServer/getCategories", params)
};

export const RefreshUploadVideo = (params) => {
    return vodHttp.get("/vodServer/refreshUploadVideo", params)
};

